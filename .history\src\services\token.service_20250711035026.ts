// src/services/tokenService.ts
import { setSecureCookie, getCookie, deleteCookie } from '../helpers/myfunc';
import { CryptoService } from './crypto.service';

interface TokenPayload {
  exp?: number;
  [key: string]: any;
}

interface EncryptedData {
  iv: string;
  cipher: string;
  authTag: string;
}

class TokenService {
  private memoryToken: string | null = null;

  /**
   * Définit le token de manière sécurisée avec cryptage
   * @param token - Le token JWT
   * @param rememberMe - Persister le token plus longtemps
   */
  async setToken(token: string, rememberMe: boolean = false): Promise<void> {
    try {
      // Cryptage du token avant stockage
      const encryptedToken = await CryptoService.encrypt(token);
      const encryptedTokenString = JSON.stringify(encryptedToken);

      // Stockage principal dans un cookie sécurisé
      setSecureCookie('auth_token', encryptedTokenString, {
        expires: rememberMe ? 168 : 8, // 7 jours ou 8 heures
        secure: true,
        sameSite: 'Strict',
        path: '/'
      });

      // Stockage secondaire en mémoire (non crypté pour performance)
      this.memoryToken = token;
    } catch (error) {
      console.error('Erreur lors du cryptage du token:', error);
      throw new Error('TOKEN_ENCRYPTION_FAILED');
    }
  }

  /**
   * Récupère le token avec décryptage
   * @returns Le token ou null si absent/invalide
   */
  async getToken(): Promise<string | null> {
    // Priorité au token en mémoire pour les performances
    if (this.memoryToken) {
      return this.memoryToken;
    }

    try {
      const encryptedTokenString = getCookie('auth_token');
      if (!encryptedTokenString) return null;

      // Décryptage du token
      const encryptedToken: EncryptedData = JSON.parse(encryptedTokenString);
      const decryptedToken = await CryptoService.decrypt(encryptedToken);

      // Mise en cache en mémoire
      this.memoryToken = decryptedToken;

      return decryptedToken;
    } catch (error) {
      console.error('Erreur lors du décryptage du token:', error);
      // Nettoyage en cas d'erreur de décryptage
      this.clearToken();
      return null;
    }
  }

  /**
   * Vérifie la validité du token
   * @returns true si le token est valide
   */
  isValid(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = this.parsePayload(token);
      return !this.isExpired(payload);
    } catch {
      this.clearToken();
      return false;
    }
  }

  /**
   * Supprime le token de tous les stockages
   */
  clearToken(): void {
    deleteCookie('auth_token');
    this.memoryToken = null;
  }

  /**
   * Récupère les données du payload
   * @returns Les données du payload ou null
   */
  getPayload(): TokenPayload | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      return this.parsePayload(token);
    } catch {
      return null;
    }
  }

  private parsePayload(token: string): TokenPayload {
    this.validateTokenFormat(token);
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    return JSON.parse(atob(base64));
  }

  private validateTokenFormat(token: string): void {
    if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(token)) {
      throw new Error('Format de token invalide');
    }
  }

  private isExpired(payload: TokenPayload): boolean {
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      this.clearToken();
      return true;
    }
    return false;
  }
}

// Exportez une instance singleton
export const tokenService = new TokenService();
