/**
 * Fonctions sécurisées pour la gestion des cookies et tokens
 * Implémente les bonnes pratiques OWASP pour la sécurité frontend
 */

interface CookieOptions {
  expires?: Date | number; // En heures ou objet Date
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
  httpOnly?: boolean; // Simulé (vrai HttpOnly doit être défini côté serveur)
}

/**
 * Définit un cookie de manière sécurisée
 * @param name - Nom du cookie
 * @param value - Valeur du cookie
 * @param options - Options de sécurité
 */
function setSecureCookie(
  name: string,
  value: string,
  options: CookieOptions = {}
): void {
  if (typeof document === 'undefined') return; // Exécution côté serveur

  // Validation des entrées
  if (!name || !/^[a-zA-Z0-9_-]+$/.test(name)) {
    throw new Error('Nom de cookie invalide');
  }

  // Chiffrement de base (à remplacer par un vrai chiffrement en production)
  const encodedValue = encodeURIComponent(value);

  let cookieString = `${name}=${encodedValue}`;

  // Gestion de l'expiration
  if (options.expires) {
    const expires = typeof options.expires === 'number'
      ? new Date(Date.now() + options.expires * 3600000)
      : options.expires;
    cookieString += `; expires=${expires.toUTCString()}`;
  }

  // Options de sécurité
  cookieString += `; path=${options.path || '/'}`;

  if (options.domain) {
    cookieString += `; domain=${options.domain}`;
  }

  if (options.secure || location.protocol === 'https:') {
    cookieString += '; Secure';
  }

  if (options.sameSite) {
    cookieString += `; SameSite=${options.sameSite}`;
  }

  // httpOnly ne peut pas être défini en JS (côté serveur seulement)
  document.cookie = cookieString;
}

/**
 * Récupère un cookie de manière sécurisée
 * @param name - Nom du cookie
 * @returns Valeur décodée ou null
 */
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;

  const cookies = document.cookie.split('; ');
  for (const cookie of cookies) {
    const [cookieName, cookieValue] = cookie.split('=');
    if (cookieName === name) {
      return decodeURIComponent(cookieValue);
    }
  }
  return null;
}

/**
 * Supprime un cookie de manière sécurisée
 * @param name - Nom du cookie
 * @param path - Chemin du cookie
 * @param domain - Domaine du cookie
 */
function deleteCookie(
  name: string,
  path: string = '/',
  domain?: string
): void {
  if (typeof document === 'undefined') return;

  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;

  if (domain) {
    cookieString += `; domain=${domain}`;
  }

  if (location.protocol === 'https:') {
    cookieString += '; Secure';
  }

  document.cookie = cookieString;
}

/**
 * Gestion sécurisée des tokens d'authentification
 */
const tokenManager = {
  /**
   * Stocke le token de manière sécurisée
   * @param token - Token JWT
   * @param rememberMe - Conserver le token plus longtemps
   */
  setToken(token: string, rememberMe: boolean = false): void {
    // Stockage principal dans un cookie sécurisé
    setSecureCookie('auth_token', token, {
      expires: rememberMe ? 24 * 7 : 8, // 1 semaine ou 8 heures
      secure: true,
      sameSite: 'Strict'
    });

    // Stockage secondaire en mémoire (optionnel)
    if (typeof window !== 'undefined') {
      window.__PRIVATE_AUTH_TOKEN__ = token; // Non persistant
    }
  },

  /**
   * Récupère le token de manière sécurisée
   * @returns Token ou null
   */
  getToken(): string | null {
    // 1. Essaye de récupérer depuis les cookies
    const cookieToken = getCookie('auth_token');
    if (cookieToken) return cookieToken;

    // 2. Fallback en mémoire (le moins sécurisé)
    if (typeof window !== 'undefined' && window.__PRIVATE_AUTH_TOKEN__) {
      return window.__PRIVATE_AUTH_TOKEN__;
    }

    return null;
  },

  /**
   * Vérifie la validité du token
   * @returns true si le token est valide
   */
  checkToken(): boolean {
    const token = this.getToken();
    if (!token) return false;

    // Vérification basique du format JWT
    if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(token)) {
      this.clearToken();
      return false;
    }

    // Vérification de l'expiration (si incluse dans le token)
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      if (payload.exp && payload.exp * 1000 < Date.now()) {
        this.clearToken();
        return false;
      }
      return true;
    } catch {
      this.clearToken();
      return false;
    }
  },

  /**
   * Supprime tous les tokens
   */
  clearToken(): void {
    deleteCookie('auth_token');
    if (typeof window !== 'undefined') {
      delete window.__PRIVATE_AUTH_TOKEN__;
      localStorage.removeItem('auth_token_fallback');
    }
  }
};

/**
 * Génère une chaîne aléatoire sécurisée
 * @param length - Longueur de la chaîne
 * @returns Chaîne aléatoire
 */
function generateSecureRandom(length: number = 32): string {
  const crypto = window.crypto || (window as any).msCrypto;
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';

  if (crypto?.getRandomValues) {
    const values = new Uint32Array(length);
    crypto.getRandomValues(values);
    for (let i = 0; i < length; i++) {
      result += charset[values[i] % charset.length];
    }
  } else {
    // Fallback moins sécurisé
    console.warn('Crypto API non disponible, utilisation de Math.random()');
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
  }

  return result;
}

/**
 * Utilitaires de date sécurisés
 */
const dateUtils = {
  addHours(date: Date, hours: number): Date {
    const result = new Date(date);
    result.setTime(result.getTime() + hours * 60 * 60 * 1000);
    return result;
  },

  getExpiresIn(hours: number): string {
    return `expires=${this.addHours(new Date(), hours).toUTCString()}`;
  },

  calculateTimeDifference(start: Date, end: Date): { hours: number; minutes: number } {
    const diff = end.getTime() - start.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return { hours, minutes };
  }
};

export {
  setSecureCookie as setCookie,
  getCookie,
  deleteCookie,
  tokenManager,
  generateSecureRandom as random,
  dateUtils
};
