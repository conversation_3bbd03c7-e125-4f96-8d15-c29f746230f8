// src/services/tokenService.ts
import { setSecureCookie, getCookie, deleteCookie } from '../helpers/myfunc';
import { CryptoService } from './crypto.service';

interface TokenPayload {
  exp?: number;
  [key: string]: any;
}

interface EncryptedData {
  iv: string;
  cipher: string;
  authTag: string;
}

class TokenService {
  private memoryToken: string | null = null;

  /**
   * Définit le token de manière sécurisée avec cryptage
   * @param token - Le token JWT
   * @param rememberMe - Persister le token plus longtemps
   */
  async setToken(token: string, rememberMe: boolean = false): Promise<void> {
    try {
      console.log('🔐 Début du stockage du token...');

      // TEMPORAIRE: Stockage direct sans cryptage pour debug
      // TODO: Réactiver le cryptage une fois le problème résolu
      /*
      const encryptedToken = await CryptoService.encrypt(token);
      const encryptedTokenString = JSON.stringify(encryptedToken);
      */
      const tokenToStore = token; // Stockage direct temporaire
      console.log('✅ Token préparé pour stockage (sans cryptage temporaire)');

      // Stockage principal dans un cookie sécurisé
      setSecureCookie('auth_token', tokenToStore, {
        expires: rememberMe ? 168 : 8, // 7 jours ou 8 heures
        secure: location.protocol === 'https:', // Seulement en HTTPS
        sameSite: 'Strict',
        path: '/'
      });
      console.log('✅ Token stocké dans le cookie');

      // Stockage secondaire en mémoire (non crypté pour performance)
      this.memoryToken = token;
      console.log('✅ Token stocké en mémoire');
    } catch (error) {
      console.error('❌ Erreur lors du cryptage du token:', error);
      throw new Error('TOKEN_ENCRYPTION_FAILED');
    }
  }

  /**
   * Récupère le token avec décryptage
   * @returns Le token ou null si absent/invalide
   */
  async getToken(): Promise<string | null> {
    console.log('🔍 Récupération du token...');

    // Priorité au token en mémoire pour les performances
    if (this.memoryToken) {
      console.log('✅ Token trouvé en mémoire');
      return this.memoryToken;
    }

    try {
      const tokenString = getCookie('auth_token');
      if (!tokenString) {
        console.log('❌ Aucun token trouvé dans les cookies');
        return null;
      }
      console.log('✅ Token trouvé dans les cookies');

      // TEMPORAIRE: Récupération directe sans décryptage pour debug
      // TODO: Réactiver le décryptage une fois le problème résolu
      /*
      const encryptedToken: EncryptedData = JSON.parse(tokenString);
      const decryptedToken = await CryptoService.decrypt(encryptedToken);
      */
      const token = tokenString; // Récupération directe temporaire
      console.log('✅ Token récupéré avec succès (sans décryptage temporaire)');

      // Mise en cache en mémoire
      this.memoryToken = token;

      return token;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du token:', error);
      // Nettoyage en cas d'erreur
      this.clearToken();
      return null;
    }
  }

  /**
   * Vérifie la validité du token
   * @returns true si le token est valide
   */
  async isValid(): Promise<boolean> {
    const token = await this.getToken();
    if (!token) return false;
    console.log('isValid', token, this.memoryToken);

    try {
      // Vérifier si c'est un token Laravel Sanctum (format: id|token)
      if (this.isSanctumToken(token)) {
        console.log("Token Sanctum détecté, considéré comme valide");
        // Pour les tokens Sanctum, on considère qu'ils sont valides s'ils existent
        // La validation réelle se fait côté serveur
        return true;
      }

      // Si c'est un JWT, on peut vérifier l'expiration
      const payload = this.parsePayload(token);
      console.log("Payload JWT:", payload);
      return !this.isExpired(payload);
    } catch (error) {
      console.error("Erreur lors de la validation du token:", error);
      // this.clearToken();
      return false;
    }
  }

  /**
   * Supprime le token de tous les stockages
   */
  clearToken(): void {
    deleteCookie('auth_token');
    this.memoryToken = null;
  }

  /**
   * Récupère les données du payload
   * @returns Les données du payload ou null
   */
  async getPayload(): Promise<TokenPayload | null> {
    const token = await this.getToken();
    if (!token) return null;

    try {
      // Les tokens Sanctum n'ont pas de payload décodable
      if (this.isSanctumToken(token)) {
        console.log("Token Sanctum: pas de payload décodable");
        return null;
      }

      return this.parsePayload(token);
    } catch {
      return null;
    }
  }

  /**
   * Vérifie si le token est un token Laravel Sanctum
   * @param token Le token à vérifier
   * @returns true si c'est un token Sanctum
   */
  private isSanctumToken(token: string): boolean {
    // Format Laravel Sanctum: {id}|{token}
    return /^\d+\|[A-Za-z0-9]+$/.test(token);
  }

  private parsePayload(token: string): TokenPayload {
    this.validateTokenFormat(token);
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    return JSON.parse(atob(base64));
  }

  private validateTokenFormat(token: string): void {
    if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(token)) {
      throw new Error('Format de token invalide');
    }
  }

  private isExpired(payload: TokenPayload): boolean {
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      this.clearToken();
      return true;
    }
    return false;
  }
}

// Exportez une instance singleton
export const tokenService = new TokenService();
