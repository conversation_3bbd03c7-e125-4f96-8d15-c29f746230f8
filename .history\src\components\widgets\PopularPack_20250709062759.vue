<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { adminStore } from 'src/stores/core/adminStore';

interface PopularPack {
  id: number
  pack_name: string
  carnet_price: number
  total_subscriptions: number
  successful_subscriptions: string
  subscription_revenue: number
  total_cotisations: number
  completed_cotisations: string
  cotisation_revenue: number
  performance_score: number
}

export default defineComponent({
  name: 'PopularPack',
  setup() {
    const packs = ref<PopularPack[]>([]);
    const loading = ref(false);
    const store = adminStore();

    // Fonction pour formater les montants en FCFA
    const formatRevenue = (amount: number): string => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XOF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount)
    }

    // Fonction pour calculer le taux de réussite
    const getSuccessRate = (pack: PopularPack): number => {
      const successful = parseInt(pack.successful_subscriptions)
      return pack.total_subscriptions > 0 ? (successful / pack.total_subscriptions) * 100 : 0
    }

    // Fonction pour obtenir la couleur du badge selon le score de performance
    const getPerformanceColor = (score: number): string => {
      if (score >= 1000) return 'green'
      if (score >= 500) return 'orange'
      if (score >= 200) return 'blue'
      return 'grey'
    }

    // Fonction pour tronquer le nom du pack
    const truncatePackName = (name: string, maxLength: number = 60): string => {
      return name.length > maxLength ? name.substring(0, maxLength) + '...' : name
    }

    const getPacks = async () => {
      loading.value = true;
      try {
        const response = await store.getPopularPacks();
        console.log("popular packs",response);
        if (response.success) {
          packs.value = response.result;
        }

      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      getPacks();
    });

    return {
      packs,
      loading,
      formatRevenue,
      getSuccessRate,
      getPerformanceColor,
      truncatePackName,
      getPacks
    };

  },
})
</script>

<template>
  <q-card class="q-pa-md" flat>
    <div class="row items-center q-mb-md">
      <div class="col">
        <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">
          <q-icon name="trending_up" class="q-mr-sm text-green" />
          Produits les plus demandés
        </h5>
        <p class="text-caption text-grey-6 q-ma-none">Les articles que nos clients préfèrent</p>
      </div>
      <div class="col-auto">
        <q-btn
          flat
          dense
          round
          icon="refresh"
          @click="getPacks()"
          :loading="loading"
          color="grey-6"
          size="sm"
        >
          <q-tooltip>Actualiser la liste</q-tooltip>
        </q-btn>
      </div>
    </div>

    <!-- Spinner de chargement -->
    <div v-if="loading" class="flex flex-center q-py-xl">
      <q-spinner-dots color="primary" size="40px" />
    </div>

    <!-- Liste des packs -->
    <q-scroll-area v-else-if="packs.length > 0" style="height: 600px;" class="q-pr-sm">
      <div class="q-gutter-sm">
        <q-card
          v-for="(pack, index) in packs"
          :key="pack.id"
          class="pack-card"
          flat
          bordered
        >
          <q-card-section class="q-pa-md">
            <!-- En-tête simplifié -->
            <div class="row items-center q-mb-md">
              <div class="col-auto">
                <q-avatar
                  :color="index < 3 ? 'amber' : index < 5 ? 'blue' : 'grey-5'"
                  :text-color="index < 3 ? 'black' : 'white'"
                  size="32px"
                  class="text-weight-bold"
                >
                  {{ index + 1 }}
                </q-avatar>
              </div>
              <div class="col q-ml-sm">
                <div class="text-body2 text-weight-medium text-grey-8">
                  {{ truncatePackName(pack.pack_name, 80) }}
                </div>
                <div class="text-caption text-grey-6">
                  Prix : {{ formatRevenue(pack.carnet_price) }} par carnet
                </div>
              </div>
            </div>

            <!-- Informations principales en format simple -->
            <div class="bg-grey-1 rounded-borders q-pa-md q-mb-sm">
              <div class="row items-center q-mb-sm">
                <div class="col">
                  <div class="text-body2 text-grey-7">
                    <q-icon name="people" class="q-mr-xs" />
                    <strong>{{ pack.total_subscriptions }}</strong> personnes intéressées
                  </div>
                </div>
              </div>

              <div class="row items-center q-mb-sm">
                <div class="col">
                  <div class="text-body2 text-grey-7">
                    <q-icon name="check_circle" class="q-mr-xs text-green" />
                    <strong class="text-green">{{ pack.successful_subscriptions }}</strong> ont terminé leur épargne
                  </div>
                </div>
              </div>

              <div class="row items-center">
                <div class="col">
                  <div class="text-body2 text-grey-7">
                    <q-icon name="percent" class="q-mr-xs" />
                    <strong>{{ getSuccessRate(pack).toFixed(0) }}%</strong> de réussite
                  </div>
                </div>
                <div class="col-auto">
                  <q-badge
                    :color="getSuccessRate(pack) >= 50 ? 'green' : getSuccessRate(pack) >= 25 ? 'orange' : 'red'"
                    :label="getSuccessRate(pack) >= 50 ? 'Excellent' : getSuccessRate(pack) >= 25 ? 'Bon' : 'Moyen'"
                    rounded
                  />
                </div>
              </div>
            </div>

            <!-- Barre de progression simple -->
            <div class="q-mb-md">
              <div class="text-caption text-grey-6 q-mb-xs">Taux de réussite</div>
              <q-linear-progress
                :value="getSuccessRate(pack) / 100"
                :color="getSuccessRate(pack) >= 50 ? 'green' : getSuccessRate(pack) >= 25 ? 'orange' : 'red'"
                size="12px"
                rounded
                stripe
              />
            </div>

            <!-- Revenus simplifiés -->
            <div class="row q-gutter-sm">
              <div class="col-12">
                <div class="bg-blue-1 rounded-borders q-pa-sm text-center">
                  <div class="text-caption text-blue-8">Revenus totaux générés</div>
                  <div class="text-h6 text-weight-bold text-blue-8">
                    {{ formatRevenue(Number(pack.subscription_revenue) + Number(pack.cotisation_revenue)) }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Indicateur de popularité -->
            <div class="row items-center q-mt-sm" v-if="index < 5">
              <div class="col">
                <q-chip
                  :color="index === 0 ? 'red' : index < 3 ? 'orange' : 'blue'"
                  :text-color="'white'"
                  :icon="index === 0 ? 'local_fire_department' : index < 3 ? 'trending_up' : 'thumb_up'"
                  :label="index === 0 ? 'Le plus demandé !' : index < 3 ? 'Très populaire' : 'Populaire'"
                  dense
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </q-scroll-area>

    <!-- Message si pas de données -->
    <div v-else class="text-center text-grey-5 q-py-xl">
      <q-icon name="shopping_cart" size="64px" class="q-mb-md" />
      <div class="text-h6 q-mb-sm">Aucun produit disponible</div>
      <div class="text-body2">Les produits les plus demandés apparaîtront ici</div>
    </div>
  </q-card>
</template>

<style scoped>
.pack-card {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.pack-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-left-color: var(--q-primary);
}

/* Amélioration de la lisibilité */
.text-caption {
  font-size: 0.75rem;
  font-weight: 500;
}

.text-body2 {
  line-height: 1.5;
}

.text-h6 {
  font-size: 1.1rem;
}

/* Styles pour les avatars de rang */
.q-avatar {
  font-size: 0.9rem;
}

/* Amélioration des barres de progression */
.q-linear-progress {
  border-radius: 6px;
  overflow: hidden;
}

/* Styles pour les zones d'information */
.bg-grey-1 {
  border: 1px solid #f0f0f0;
}

.bg-blue-1 {
  border: 1px solid #e3f2fd;
}

/* Amélioration des chips */
.q-chip {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 600px) {
  .pack-card .row.q-gutter-sm {
    flex-direction: column;
  }

  .pack-card .col {
    margin-bottom: 8px;
  }

  .text-h6 {
    font-size: 1rem;
  }
}

/* Animation douce pour les éléments interactifs */
.q-btn, .q-chip, .q-badge {
  transition: all 0.2s ease;
}

.q-btn:hover {
  transform: scale(1.05);
}
</style>

