// src/utils/dateUtils.ts

export const dateUtils = {
  addHours(date: Date, hours: number): Date {
    const result = new Date(date);
    result.setTime(result.getTime() + hours * 60 * 60 * 1000);
    return result;
  },

  getExpiresIn(hours: number): string {
    return `expires=${this.addHours(new Date(), hours).toUTCString()}`;
  },

  calculateTimeDifference(start: Date, end: Date): { hours: number; minutes: number } {
    const diff = end.getTime() - start.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return { hours, minutes };
  }

  
};
