import { defineStore } from 'pinia'
import { postData, postDataWithToken,getDataWithToken, getDataWithParams, postDataWithFile } from '../../helpers/http';
import { Category, Product, Response } from 'src/models';
import { api } from 'src/router/api';


// @ts-ignore
let categoriesData = JSON.parse(localStorage.getItem('categories')) as Category[];
export const productStore = defineStore('product', {
  // other options...
  state: () => ({
    loading: false,
    products: []  as Product[],
    alertProducts: []  as Product[],
    product: {} as Product,
    categories: categoriesData || [] as Category[],
  }),
  persist: true,

  getters: {
    get_products : (state) => {
      return state.products;
    },

    get_product_byId: (state) => {
      return (id: number) => {
        return state.products.find((product) => product.id === id);
      }
    },

    get_categories: (state) => {
      return state.categories;
    },

    get_products_byCategory: (state) => {
      return (category_id: number) => {
        return state.products.filter((product) => product.category_id === category_id);
      }
    }
  },

  actions: {
    async getCategories(): Promise<Response> {
      try {
        const response = await getDataWithToken(api.admin.categories.all);
        if (response.success) {
          this.categories = response.result;
          localStorage.setItem('categories', JSON.stringify(response.result));
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },


    async addCategories(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.categories.add, payload);

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        }
      }
    },

    async updateCategory(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.categories.update, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        }
      }
    },

    async getProducts(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.products.all,payload);
        if (response.success) {
          this.products = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async addProduct(payload: any): Promise<Response> {
      try {
        const response = await postDataWithFile(api.admin.products.add,payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async updateProduct(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.products.update,payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async getDetailProduct(product_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.products.detail,{
          product_id: product_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async getAlertProducts(): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.products.alerts);
        if (response.success) {
          this.alertProducts = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async getProductPack(product_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.products.packs,{
          product_id: product_id
        });

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async getProductSubscription(product_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.products.clients_subscribe,{
          product_id: product_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },

    async getProductCotisations(product_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.products.cotisations,{
          product_id: product_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        }
      }
    },





  }

});
