<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  export default defineComponent({
    name: "DeliveryPage",
    components: {
      BreadCrumb,
    },
    setup(){
      const bread = ref({
        pageTitle: "Livraisons",
        subTitle: "Gestion des livraisons de carnets"
      });
      const tab = ref('tab1');

      return {
        bread,tab
      };
    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat>
          <q-toolbar class="bg-primary text-white shadow-2 rounded-borders">
            <q-btn flat label="Gestion des livraisons" />
            <q-space />
            <q-tabs v-model="tab" shrink stretch>
              <q-tab name="tab1" label="Clôturer" />
              <q-tab name="tab2" label="En cours" />
              <q-tab name="tab3" label="En attente" />
            </q-tabs>
          </q-toolbar>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
