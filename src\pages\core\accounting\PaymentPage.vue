<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import PaymentTable from 'src/components/tables/PaymentTable.vue';
  export default defineComponent({
    name: "SubscriptionPage",
    components: {
      BreadCrumb,PaymentTable
    },
    setup(){

      const bread = ref({
        pageTitle: "Souscriptions",
        subTitle: "Gestion des paiements"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Etat des paiements
            </q-toolbar-title>
            <!-- <q-btn flat  dense icon="account_circle" label="AJOUTER CAISSIER" class="q-mr-xs" /> -->
          </q-toolbar>
          <PaymentTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
