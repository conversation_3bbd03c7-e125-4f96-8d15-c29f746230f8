import { defineStore } from 'pinia'
import { postDataWithToken, getDataWithToken, getDataWithParams } from '../../helpers/http';
import {
  Agency, City, Client, Country, Personal, Quarter, Role, User, Response,
  Versement, Subscription, Payment, Transaction, Analytic, Control,
  Cotisation,
  FinancialHealth
} from 'src/models';
import { apiURL } from 'src/router/api';

// @ts-ignore
let countriesData = JSON.parse(localStorage.getItem('countries')) as Country[];
// @ts-ignore
let citiesData = JSON.parse(localStorage.getItem('cities')) as City[];
// @ts-ignore
let quartersData = JSON.parse(localStorage.getItem('quarters')) as Quarter[];
// @ts-ignore
let rolesData = JSON.parse(localStorage.getItem('roles')) as Role[];


export const adminStore = defineStore('admin', {
  // other options...
  state: () => ({
    loading: false,
    agencies: [] as Agency[],
    agency: {} as Agency,
    personals: [] as Personal[],
    personal: {} as Personal,
    countries: countriesData || [] as Country[],
    cities: citiesData || [] as City[],
    quarters: quartersData || [] as Quarter[],
    roles: rolesData || [] as Role[],
    clients: [] as Client[],
    users: [] as User[],
    cashiers: [] as any[],
    versements: [] as Versement[],
    subscriptions: [] as Subscription[],
    payments: [] as Payment[],
    transactions: [] as Transaction[],
    analytics: {} as Analytic,
    controls: [] as Control[],
    cotisations: [] as Cotisation[],
    financialHealth: {} as FinancialHealth,
  }),
  persist: {
    key: 'admin',
    storage: localStorage
  },

  getters: {
    get_agencies: (state) => {
      return state.agencies;
    },

    get_agency: (state) => {
      return (code: string) => {
        return state.agencies.find((agency) => agency.code === code);
      }
    },


  },

  actions: {
    /**
     * getAgencies function
     * @returns {Promise<Response>}
     */
    async getAgencies(payload: any): Promise<Response> {
      try {
        const response = await getDataWithParams(apiURL.admin.agency.all, payload);
        if (response.success) {
          this.agencies = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    /**
     * getDetailAgency function
     * Retourne les détails d'une agence
     * @param id
     * @param id
     * @returns {Promise<Response>}
     */
    async getDetailAgency(id: number): Promise<Response> {
      try {
        const response = await getDataWithParams(apiURL.admin.agency.detail, {
          agency_id: id
        });
        if (response.success) {
          this.agency = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    /**
     * createAgency function
     * @param payload
     * @returns {Promise<Response>}
     */
    async createAgency(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(apiURL.admin.agency.create, payload);
        if (response.success) {
          this.agencies.push(response.result);
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    /**
     * Update an agency using the provided payload.
     *
     * @param {any} payload - The data for creating the agency.
     * @return {Promise<Response>} - The response from the API.
     */
    async updateAgency(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(apiURL.admin.agency.update, payload);

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getRoles(): Promise<Response> {
      try {
        const response = await getDataWithToken(apiURL.admin.personals.roles);
        if (response.success) {
          this.roles = response.result;
          localStorage.setItem('roles', JSON.stringify(this.roles));
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getCountries(): Promise<Response> {
      try {
        const response = await getDataWithToken(apiURL.common.countries.all);
        if (response.success) {
          this.countries = response.result;
          localStorage.setItem('countries', JSON.stringify(this.countries));
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getCities(): Promise<Response> {
      try {
        const response = await getDataWithToken(apiURL.common.cities.all);
        if (response.success) {
          this.cities = response.result;
          localStorage.setItem('cities', JSON.stringify(this.cities));
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getQuarters(): Promise<Response> {
      try {
        const response = await getDataWithToken(api.common.quarters.all);
        if (response.success) {
          this.quarters = response.result;
          localStorage.setItem('quarters', JSON.stringify(this.quarters));
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async addCountry(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.common.countries.add, payload);
        if (response.success) {
          this.countries.push(response.result);
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async addCities(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.common.cities.add, payload);
        if (response.success) {
          this.cities.push(response.result);
        }
        return response;
      } catch (error) {
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async addQuarters(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.common.quarters.add, payload);
        if (response.success) {
          this.quarters.push(response.result);
        }
        return response;
      } catch (error) {
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getCashiers(): Promise<Response> {
      try {
        const response = await getDataWithToken(api.admin.accounting.cashiers);
        if (response.success) {
          this.cashiers = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getVersements(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.accounting.versements, payload);
        if (response.success) {
          this.versements = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getSubscriptions(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.accounting.subscriptions,payload);
        if (response.success) {
          this.subscriptions = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getCotisations(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.accounting.cotisations, payload);
        if (response.success) {
          this.cotisations = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getPayments(): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.accounting.payments);
        if (response.success) {
          this.payments = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getTransactions(): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.accounting.transactions);
        if (response.success) {
          this.transactions = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getDashboard(): Promise<Response> {
      try {
        const response = await getDataWithToken(api.admin.dashboard);
        if (response.success) {
          this.analytics = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getFinancialHealth(): Promise<Response> {
      try {
        const response = await getDataWithToken(api.admin.analitycs.financial_health);
        if (response.success) {
          this.financialHealth = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getPaymentByPeriod(period: string): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.payments, { period: period });

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getCarnetInactif(): Promise<Response> {
      try {
        const response = await getDataWithToken(api.admin.analitycs.carnet_inactifs);

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getLastClientByPeriod(period: string): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.last_clients, { period: period });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getSubscriptionsByPeriod(period: string): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.subscriptions, { period: period });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getSubscriptionsByDate(date: string): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.subscriptions, { date: date });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getSubscriptionsByFilter(filter: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.subscriptions, filter);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getVersementsByPeriod(period: string): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.versements, { period: period });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getControls(): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.accounting.controls);
        if (response.success) {
          this.controls = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getFinancialTrends(payload: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.financial_trends, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getTopCollectors(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.top_collectors, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getTopClients(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.analitycs.top_clients, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getPopularPacks(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithToken(api.admin.analitycs.popular_packs, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },




  },

});
