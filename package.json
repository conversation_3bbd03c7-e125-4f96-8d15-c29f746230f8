{"name": "admin-dashboard", "version": "0.0.1", "description": "iziCollet administration panel", "productName": "iziCollect", "author": "Bonaventoura", "private": true, "scripts": {"test": "echo \"No test specified\" && exit 0", "dev": "quasar dev -m pwa", "build": "quasar build -m pwa"}, "dependencies": {"@quasar/extras": "^1.0.0", "apexcharts": "^3.51.0", "axios": "^1.2.1", "chart.js": "^4.3.0", "dotenv": "^17.2.0", "lucide-vue-next": "^0.454.0", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^3.2.0", "quasar": "^2.6.0", "vue": "^3.0.0", "vue-chartjs": "^5.2.0", "vue-i18n": "^9.2.2", "vue-router": "^4.0.0", "vue3-apexcharts": "^1.5.3", "vue3-charts": "^1.1.33", "zod": "^4.0.5"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.0.0", "@types/node": "^12.20.21", "autoprefixer": "^10.4.2", "typescript": "^4.5.4", "workbox-build": "^6.6.0", "workbox-cacheable-response": "^6.6.0", "workbox-core": "^6.6.0", "workbox-expiration": "^6.6.0", "workbox-precaching": "^6.6.0", "workbox-routing": "^6.6.0", "workbox-strategies": "^6.6.0"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}