<script lang="ts">
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent,ref,onMounted } from 'vue'

interface TopClient {
  id: number
  nom: string
  prenoms: string
  phone: string
  finished_subscriptions_count: number
  subscription_revenue: number
  cotisation_revenue: number
  total_revenue: number
  rank?: number
}

export default defineComponent({
  name: 'TopClientByRevenue',
  setup() {
    const clients = ref<TopClient[]>([]);
    const loading = ref(false);
    const store = adminStore();

    const getClients = async () => {
      loading.value = true;
      try {
        const response = await store.getTopClients({year:2025});
        // console.log("top clients",response);
        if (response.success) {
          // Ajouter le rang à chaque client
          clients.value = response.result.map((client: TopClient, index: number) => ({
            ...client,
            rank: index + 1
          }));
        }

      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    };

    // Fonction pour formater le montant en FCFA
    const formatRevenue = (amount: number): string => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XOF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount)
    }

    // Définition des colonnes de la table
    const columns = [
      {
        name: 'rank',
        label: 'Rang',
        field: 'rank',
        align: 'center' as const,
        sortable: false
      },
      {
        name: 'name',
        label: 'Nom complet',
        field: (row: any) => `${row.prenoms} ${row.nom}`,
        align: 'left' as const,
        sortable: true
      },
      {
        name: 'phone',
        label: 'Téléphone',
        field: 'phone',
        align: 'left' as const,
        sortable: false
      },
      {
        name: 'subscriptions',
        label: 'Carnets vendus',
        field: 'finished_subscriptions_count',
        align: 'left' as const,
        sortable: true
      },
      {
        name: 'subscription_revenue',
        label: 'CA Carnets',
        field: 'subscription_revenue',
        align: 'left' as const,
        sortable: true,
        format: (val: number) => formatRevenue(val)
      },
      {
        name: 'cotisation_revenue',
        label: 'CA Cotisations',
        field: 'cotisation_revenue',
        align: 'left' as const,
        sortable: true,
        format: (val: number) => formatRevenue(val)
      },
      {
        name: 'total_revenue',
        label: 'CA Total',
        field: 'total_revenue',
        align: 'left' as const,
        sortable: true,
        format: (val: number) => formatRevenue(val)
      }
    ]

    onMounted(() => {
      getClients();
    });

    return {
      clients,
      loading,
      columns,
      formatRevenue
    }
  },
})
</script>

<template>
  <q-card class="q-pa-md" flat>
    <div class="row items-center q-mb-md">
      <div class="col">
        <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Top 10 des Clients par recette</h5>
      </div>
    </div>

    <q-table
      :rows="clients"
      :columns="columns"
      :loading="loading"
      row-key="id"
      flat
      bordered
      dense
      :rows-per-page-options="[10, 25, 50]"
      :pagination="{ rowsPerPage: 10 }"
      class="q-mt-md"
    >
      <!-- Slot pour personnaliser l'affichage du rang -->
      <template v-slot:body-cell-rank="props">
        <q-td :props="props">
          <q-badge
            :color="props.rowIndex < 3 ? 'amber' : 'grey-5'"
            :text-color="props.rowIndex < 3 ? 'black' : 'white'"
            :label="props.value"
            rounded
          />
        </q-td>
      </template>

      <!-- Slot pour personnaliser l'affichage du nom -->
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <div class="text-weight-medium">{{ props.value }}</div>
        </q-td>
      </template>

      <!-- Slot pour personnaliser l'affichage du téléphone -->
      <template v-slot:body-cell-phone="props">
        <q-td :props="props">
          <q-chip
            dense
            color="blue-1"
            text-color="blue-8"
            icon="phone"
            :label="props.value"
          />
        </q-td>
      </template>

      <!-- Slot pour personnaliser l'affichage des carnets vendus -->
      <template v-slot:body-cell-subscriptions="props">
        <q-td :props="props">
          <q-badge
            color="green"
            :label="props.value"
            rounded
          />
        </q-td>
      </template>

      <!-- Slot pour personnaliser l'affichage du CA Total -->
      <template v-slot:body-cell-total_revenue="props">
        <q-td :props="props">
          <div class="text-weight-bold text-positive">{{ props.value }}</div>
        </q-td>
      </template>

      <!-- Message si pas de données -->
      <template v-slot:no-data>
        <div class="full-width row flex-center text-grey-5 q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>Aucun client trouvé</span>
        </div>
      </template>
    </q-table>
  </q-card>
</template>

