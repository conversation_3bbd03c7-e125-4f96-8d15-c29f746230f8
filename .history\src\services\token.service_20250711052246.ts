// src/services/tokenService.ts
import { setSecureCookie, getCookie, deleteCookie } from '../helpers/myfunc';
import { CryptoService } from './crypto.service';

interface TokenPayload {
  exp?: number;
  [key: string]: any;
}

interface EncryptedData {
  iv: string;
  cipher: string;
  authTag: string;
}

class TokenService {
  private memoryToken: string | null = null;

  /**
   * Définit le token de manière sécurisée avec cryptage
   * @param token - Le token JWT
   * @param rememberMe - Persister le token plus longtemps
   */
  async setToken(token: string, rememberMe: boolean = false): Promise<void> {
    try {
      console.log('🔐 Début du stockage du token...');

      // Cryptage du token avant stockage
      const encryptedToken = await CryptoService.encrypt(token);
      const encryptedTokenString = JSON.stringify(encryptedToken);
      console.log('✅ Token crypté avec succès');

      // Stockage principal dans un cookie sécurisé
      setSecureCookie('auth_token', encryptedTokenString, {
        expires: rememberMe ? 168 : 8, // 7 jours ou 8 heures
        secure: location.protocol === 'https:', // Seulement en HTTPS
        sameSite: 'Strict',
        path: '/'
      });
      console.log('✅ Token stocké dans le cookie');

      // Stockage secondaire en mémoire (non crypté pour performance)
      this.memoryToken = token;
      console.log('✅ Token stocké en mémoire');
    } catch (error) {
      console.error('❌ Erreur lors du cryptage du token:', error);
      throw new Error('TOKEN_ENCRYPTION_FAILED');
    }
  }

  /**
   * Récupère le token avec décryptage
   * @returns Le token ou null si absent/invalide
   */
  async getToken(): Promise<string | null> {
    console.log('🔍 Récupération du token...');

    // Priorité au token en mémoire pour les performances
    if (this.memoryToken) {
      console.log('✅ Token trouvé en mémoire');
      return this.memoryToken;
    }

    try {
      const encryptedTokenString = getCookie('auth_token');
      if (!encryptedTokenString) {
        console.log('❌ Aucun token trouvé dans les cookies');
        return null;
      }
      console.log('✅ Token crypté trouvé dans les cookies');

      // Décryptage du token
      const encryptedToken: EncryptedData = JSON.parse(encryptedTokenString);
      const decryptedToken = await CryptoService.decrypt(encryptedToken);
      console.log('✅ Token décrypté avec succès');

      // Mise en cache en mémoire
      this.memoryToken = decryptedToken;

      return decryptedToken;
    } catch (error) {
      console.error('❌ Erreur lors du décryptage du token:', error);
      // Nettoyage en cas d'erreur de décryptage
      this.clearToken();
      return null;
    }
  }

  /**
   * Vérifie la validité du token
   * @returns true si le token est valide
   */
  async isValid(): Promise<boolean> {
    const token = await this.getToken();
    if (!token) return false;

    try {
      const payload = this.parsePayload(token);
      return !this.isExpired(payload);
    } catch {
      this.clearToken();
      return false;
    }
  }

  /**
   * Supprime le token de tous les stockages
   */
  clearToken(): void {
    deleteCookie('auth_token');
    this.memoryToken = null;
  }

  /**
   * Récupère les données du payload
   * @returns Les données du payload ou null
   */
  async getPayload(): Promise<TokenPayload | null> {
    const token = await this.getToken();
    if (!token) return null;

    try {
      return this.parsePayload(token);
    } catch {
      return null;
    }
  }

  private parsePayload(token: string): TokenPayload {
    this.validateTokenFormat(token);
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    return JSON.parse(atob(base64));
  }

  private validateTokenFormat(token: string): void {
    if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(token)) {
      throw new Error('Format de token invalide');
    }
  }

  private isExpired(payload: TokenPayload): boolean {
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      this.clearToken();
      return true;
    }
    return false;
  }
}

// Exportez une instance singleton
export const tokenService = new TokenService();
