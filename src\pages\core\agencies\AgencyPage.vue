<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import AddAgencyModal from 'src/components/modals/AddAgencyModal.vue';
  import AgenciesTable from 'src/components/tables/AgenciesTable.vue';
import Icon from 'src/components/Icon.vue';
import { House } from 'lucide-vue-next';
  export default defineComponent({
    name: "AgencyPage",
    components: {
      BreadCrumb,AddAgencyModal,AgenciesTable
    },
    setup(){
      const bread = ref({
        pageTitle: "Agences",
        subTitle: "Liste des agences"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm q-pt-xs">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat bordered >
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion des Agences
            </q-toolbar-title>
            <!-- <q-btn flat round dense icon="refresh" class="q-mr-xs" /> -->
            <AddAgencyModal />
            <House />
            <Icon name="House" size="24px" color="primary" stroke-width="2px" />
          </q-toolbar>
          <AgenciesTable />
          <q-card-section>
            <!-- <div class="text-h6">Liste des agences</div> -->
          </q-card-section>

        </q-card>
      </div>
    </div>

  </q-page>
</template>
