<script lang="ts">
  import { storeToRefs } from 'pinia';
  import {PropType, defineComponent,onMounted,ref,toRefs} from 'vue';
  import type {Client} from 'src/models';

  export default defineComponent({
    name: "CollectorClientTable",
    props:{
      clients: {
        type: Array as PropType<Client[]>,
        required: true,
      }
    },
    setup(props){
      const initialPagination = ref({
        sortBy: 'name',
        descending: false,
        page: 1,
        rowsPerPage: 5
      });
      const filter = ref('');
      const loading = ref(false);

      const {clients} = toRefs(props);

      const headers = [
        { name: 'id', label: 'ID', field: 'id', sortable: true,align:"left"},
        { name: 'nom', label: 'NOM', field: 'nom', sortable: true,align:"left" },
        { name: 'prenoms', label: 'PRENOMS ', field: 'prenoms', sortable: true,align:"left" },
        { name: 'phone', label: 'CONTACT', field: 'phone', sortable: false,align:"left" },
        { name: 'city', label: 'VILLE', field: 'city', sortable: false,align:"left" },
        { name: 'quarter', label: 'QUARTIER', field: 'quarter', sortable: true,align:"left" },
        { name: 'profession', label: 'PROFESSION', field: 'profession', sortable: true,align:"left" },
        { name: 'agency', label: 'AGENCE', field: 'agency', sortable: true,align:"left" },
        { name: 'subscriptions', label: 'CARNETS', field: 'subscriptions', sortable: true,align:"left" },
        { name: 'status', label: 'STATUT', field: 'status', sortable: true,align:"left" },
        { name: 'actions', label: 'Actions', field: 'actions', sortable: false,align:"left" },
      ] as any;
      const columns_visibles = [
        'nom','prenoms','email','phone','gender','city','quarter','agency','actions','status',
        'collector','subscriptions','cotisations_sum_total_amount','profession'
      ];

      onMounted(async()=>{
        loading.value = true;
        
        if (clients.value.length > 0) {
          loading.value = false;
        } else {
          setTimeout(() => {
            loading.value = false;
          }, 2500);
        }
      });

      return {
        initialPagination, filter, headers,columns_visibles,clients
      };

    }
  });
</script>

<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Liste des clients"
      :rows="clients"
      :columns="headers"
      row-key="name"
      :pagination="initialPagination"
      :filter="filter"
      table-style="max-width: 100%;"
      :visible-columns="columns_visibles"
    >
      <template v-slot:top-right>
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close" placeholder="Rechercher un personnel">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucun client trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="nom" :props="props">
            {{ props.row.nom }}
          </q-td>
          <q-td key="prenoms" :props="props">
            {{ props.row.prenoms }}
          </q-td>
          <q-td key="phone" :props="props">
            {{ props.row.phone }}
          </q-td>
          <q-td key="city" :props="props">
            {{ props.row.city?.name }}
          </q-td>
          <q-td key="quarter" :props="props">
            {{ props.row.quarter?.name }}
          </q-td>
          <q-td key="profession" :props="props">
            {{ props.row.profession }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          
          <q-td key="subscriptions" :props="props">
            {{ props.row?.subscriptions?.length }}
          </q-td>
          
          <q-td key="status" :props="props">
            <q-chip class="text-white"  :color="props.row.status === 'active' ? 'positive' : 'negative'">
              {{ props.row.status === 'active' ? "ACTIVE" : "INACTIVE" }}
            </q-chip>
          </q-td>
          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
            <q-btn dense flat icon="edit" color="secondary" class="q-mr-xs"/>
            <!-- <q-btn dense flat  color="negative" label="Bloquer"/> -->
          </q-td>
        </q-tr>
      </template>
  </q-table>
  </div>
</template>


