import { tokenService } from 'src/services/token.service';
import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: "/auth/login",
    name: "login",
    component: () => import('pages/auth/LoginPage.vue')
  },
  {
    name: "main",
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    meta: { middleware: requireAuth },
    children: [
      {
        name: "dashboard",
        path: '',
        component: () => import('pages/dashboard/Dashboard.vue')
      },
      {
        name: "financial",
        path: '/financial',
        component: () => import('pages/dashboard/FinancialPage.vue')
      },
      {
        name: "agencies",
        path: '/agencies',
        component: () => import('pages/agencies/AgencyPage.vue')
      },
      {
        name: "detail-agency",
        path: '/agencies/:code',
        component: () => import('pages/agencies/DetailAgencyPage.vue')
      },
      {
        name: "users",
        path: '/users',
        component: () => import('pages/users/UsersPage.vue')
      },
      {
        name: "user-detail",
        path: '/users/:userId',
        component: () => import('pages/users/UserDetailPage.vue')
      },
      {
        name: "personals",
        path: '/personals',
        component: () => import('pages/personals/PersonalPage.vue')
      },
      {
        name: "detail-personal",
        path: '/personals/:id/details',
        component: () => import('pages/personals/DetailPersonalPage.vue')
      },
      {
        name: "agency_chiefs",
        path: '/personals/agency_chiefs',
        component: () => import('pages/personals/AgencyResponsablePage.vue')
      },
      {
        name: "cashiers",
        path: '/personals/cashiers',
        component: () => import('pages/personals/CashiersPage.vue')
      },
      {
        name: "supervisors",
        path: '/personals/supervisors',
        component: () => import('pages/personals/SupervisorPage.vue')
      },
      {
        name: "collectors",
        path: '/personals/collectors',
        component: () => import('pages/personals/CollectorsPage.vue')
      },
      {
        name: "livreurs",
        path: '/personals/livreurs',
        component: () => import('pages/personals/DeliveryMenPage.vue')
      },
      {
        name: "clients",
        path: '/clients',
        component: () => import('pages/clients/ClientPage.vue')
      },
      {
        name: "detail-client",
        path: '/clients/:code',
        component: () => import('pages/clients/DetailClientPage.vue')
      },
      {
        name: "categories",
        path: '/categories',
        component: () => import('pages/products/CategoryPage.vue')
      },
      {
        name: "products",
        path: '/products',
        component: () => import('pages/products/ProductsPage.vue')
      },
      {
        name: "product-details",
        path: '/products/:slug',
        component: () => import('pages/products/ProductDetailPage.vue')
      },
      {
        name: "packs",
        path: '/packs',
        component: () => import('pages/packs/PackPage.vue')
      },
      {
        name: "detail-pack",
        path: '/packs/:code',
        component: () => import('pages/packs/DetailPackPage.vue')
      },
      {
        name: "accounting",
        path: '/accounting',
        component: () => import('pages/accounting/AccountingPage.vue')
      },
      {
        name: "deposits",
        path: '/deposits',
        component: () => import('pages/accounting/VersementPage.vue')
      },
      {
        name: "subscriptions",
        path: '/subscriptions',
        component: () => import('pages/accounting/SubscriptionPage.vue')
      },
      {
        name: "carnets",
        path: '/carnets',
        component: () => import('pages/carnets/CarnetPage.vue')
      },
      {
        name: "detail-carnet",
        path: '/carnets/:code',
        component: () => import('pages/carnets/DetailCarnetPage.vue')
      },
      {
        name: "payments",
        path: '/payments',
        component: () => import('pages/accounting/PaymentPage.vue')
      },
      {
        name: "cotisations",
        path: '/cotisations',
        component: () => import('pages/accounting/CotisationPage.vue')
      },
      {
        name: "controls",
        path: '/controls',
        component: () => import('pages/controls/ControlPage.vue')
      },
      {
        name: "countries",
        path: '/localisation/countries',
        component: () => import('pages/state/CountryPage.vue')
      },
      {
        name: "cities",
        path: '/localisation/cities',
        component: () => import('pages/state/CityPage.vue')
      },
      {
        name: "quarters",
        path: '/localisation/quarters',
        component: () => import('pages/state/DistrictPage.vue')
      },
      {
        name: "settings",
        path: '/settings',
        component: () => import('pages/settings/SettingPage.vue')
      },
      {
        name: "stocks",
        path: '/stocks',
        component: () => import('pages/products/StockPage.vue')
      },
      {
        name: "deliveries",
        path: '/deliveries',
        component: () => import('pages/deliveries/DeliveryPage.vue')
      }
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

/**
 * Vérifie si l'utilisateur est authentifié
 * @returns Promise<boolean> - true si authentifié, false sinon
 */
async function isAuthenticated(): Promise<boolean> {
  try {
    return await tokenService.isValid();
  } catch (error) {
    console.error('Erreur lors de la vérification de l\'authentification:', error);
    return false;
  }
}

/**
 * Middleware d'authentification sécurisé utilisant le TokenService
 * Vérifie la validité du token de manière asynchrone
 */
async function requireAuth(to: any, from: any, next: any) {
  try {
    const authenticated = await isAuthenticated();

    if (authenticated) {
      // Token valide, autoriser l'accès
      next();
    } else {
      // Token invalide ou expiré, rediriger vers la page de connexion
      console.warn('Accès refusé: utilisateur non authentifié, redirection vers la page de connexion');

      // Nettoyage du token invalide
      tokenService.clearToken();

      // Redirection avec paramètre de retour pour revenir après connexion
      next({
        path: '/auth/login',
        query: { redirect: to.fullPath }
      });
    }
  } catch (error) {
    // En cas d'erreur, rediriger vers la page de connexion par sécurité
    console.error('Erreur lors de la vérification du token:', error);
    tokenService.clearToken();
    next('/auth/login');
  }
}

/**
 * Middleware pour les routes publiques (déjà connecté)
 * Redirige vers le dashboard si l'utilisateur est déjà authentifié
 */
async function redirectIfAuthenticated(to: any, from: any, next: any) {
  try {
    const authenticated = await isAuthenticated();

    if (authenticated) {
      // Utilisateur déjà connecté, rediriger vers le dashboard
      next('/');
    } else {
      // Utilisateur non connecté, autoriser l'accès à la page
      next();
    }
  } catch (error) {
    console.error('Erreur lors de la vérification de l\'authentification:', error);
    // En cas d'erreur, autoriser l'accès à la page
    next();
  }
}


export default routes;
