<script lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { fabYoutube } from '@quasar/extras/fontawesome-v6';
import { menuSections } from './menus';
import { useRouter } from 'vue-router';
import { QSpinnerFacebook, useQuasar } from 'quasar';
import { tokenService } from 'src/services/token.service';
import { authStore } from 'src/stores/auth/authStore';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import { productStore } from 'src/stores/core/productStore';
import logo from 'src/assets/logo.jpg';
import MenuSection from './MenuSection.vue';

export default {
  name: 'MyLayout',
  components: {
    MenuSection,
  },

  setup() {
    const $q = useQuasar();
    const leftDrawerOpen = ref(false)
    const search = ref('');
    const router = useRouter();
    const isLoading = ref(false);
    const { logout, initialize } = authStore();

    // Référence pour l'intervalle de vérification du token
    let tokenCheckInterval: NodeJS.Timeout | null = null;

    const scrollStyles = {
      thumbStyle: {
        right: '1px',
        borderRadius: '10px',
        backgroundColor: '#eee',
        opacity: '0.5',
        width: '1px',
      },
      barStyle: {
        right: '1px',
        borderRadius: '10px',
        backgroundColor: '#eee',
        opacity: '0.5',
        width: '1px',
      },
    };

    const store = adminStore();
    const { countries, cities, quarters, roles, agencies } = storeToRefs(adminStore());
    const { categories } = storeToRefs(productStore());
    const prodStore = productStore();
    const { getCountries, getCities, getQuarters, getRoles, getAgencies } = store;

    const isDataLoaded = computed(() =>
      countries.value.length > 0 &&
      cities.value.length > 0 &&
      quarters.value.length > 0 &&
      roles.value.length > 0 &&
      agencies.value.length > 0 &&
      categories.value.length > 0
    );


    function toggleLeftDrawer() {
      leftDrawerOpen.value = !leftDrawerOpen.value
    }

    const showLoading = () => {
      if (isLoading.value) {
        $q.loading.show({
          spinner: QSpinnerFacebook,
          spinnerColor: 'yellow',

          spinnerSize: 140,
          backgroundColor: 'cyan-2',
          message: 'Chargement des données en cours, patientez quelques instants svp ...!',
          messageColor: 'black'
        });
      }
    };

    const hideLoading = () => {
      setTimeout(() => {
        $q.loading.hide();
      }, 3000);
    };

    const onLogout = async () => {
      try {
        // Arrêt de la surveillance du token
        stopTokenMonitoring();

        console.log("Déconnexion de l'utilisateur");
        const res = await logout();

        if (res.success) {
          $q.notify({
            type: "positive",
            position: "top-right",
            message: res.message || "Déconnexion réussie",
            timeout: 2500,
          });

          // Redirection immédiate vers la page de connexion
          router.push({ name: "login" });
        } else {
          $q.notify({
            type: "negative",
            position: "top-right",
            message: res.message || "Erreur lors de la déconnexion",
            timeout: 2500,
          });
        }
      } catch (error) {
        console.error('Erreur lors de la déconnexion:', error);
        $q.notify({
          type: "negative",
          position: "top-right",
          message: "Échec, une erreur interne est survenue",
          timeout: 2500,
        });

        // Redirection forcée en cas d'erreur
        // router.push({ name: "login" });
      }
    };

    /**
     * Vérifie l'authentification de manière sécurisée
     */
    const checkAuthentication = async (): Promise<boolean> => {
      try {
        const isValid = await tokenService.isValid();
        if (!isValid) {
          await logout();
          return false;
        }
        return true;
      } catch (error) {
        console.error('Erreur lors de la vérification du token:', error);
        await logout();
        return false;
      }
    };

    /**
     * Démarre la surveillance périodique du token
     */
    const startTokenMonitoring = () => {
      tokenCheckInterval = setInterval(async () => {
        const isAuthenticated = await checkAuthentication();
        if (!isAuthenticated) {
          // router.push({ name: 'login' });
          stopTokenMonitoring();
        }
      }, 30000); // 30 secondes
    };

    /**
     * Arrête la surveillance du token
     */
    const stopTokenMonitoring = () => {
      if (tokenCheckInterval) {
        clearInterval(tokenCheckInterval);
        tokenCheckInterval = null;
      }
    };

    const initData = async () => {
      if (!isDataLoaded.value) {  // Ne chargez que si les données sont vides
        isLoading.value = true;
        showLoading();
        try {
          // Initialisation du store d'authentification avec les services sécurisés
          await initialize();

          // Chargement des données en parallèle
          await Promise.all([
            getCountries(),
            getCities(),
            getQuarters(),
            getRoles(),
            getAgencies({ page: 1, limit: 50 }),
            prodStore.getCategories()
          ]);

          console.log("Données initialisées avec succès");
        } catch (error) {
          console.error("Erreur lors de l'initialisation des données:", error);
          $q.notify({
            type: 'negative',
            message: 'Erreur lors du chargement des données',
            position: 'top-right'
          });
        } finally {
          isLoading.value = false;
          hideLoading();
        }
      } else {
        console.log("Les données sont déjà chargées dans le store");
      }
    };


    onMounted(async () => {
      window.document.title = "Dashboard";

      // Vérification de l'authentification avec le TokenService
      const isAuthenticated = await checkAuthentication();
      if (!isAuthenticated) {
        router.push({ name: 'login' });
        return;
      }

      // Initialisation des données si l'utilisateur est authentifié
      try {
        await initData();
        // Démarrage de la surveillance du token
        startTokenMonitoring();
      } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        $q.notify({
          type: 'negative',
          message: 'Erreur lors de l\'initialisation de l\'application',
          position: 'top-right'
        });
      }
    });

    // Nettoyage lors de la destruction du composant
    onUnmounted(() => {
      stopTokenMonitoring();
    });

    return {
      fabYoutube, leftDrawerOpen, search, menuSections, logo,
      toggleLeftDrawer, onLogout, scrollStyles
    };
  }
}
</script>

<template>
  <q-layout view="lHr LpR fFf" class="bg-grey-1">
    <q-header class="bg-white text-grey-8 q-py-xs" height-hint="58">
      <q-toolbar>
        <q-btn flat dense round @click="toggleLeftDrawer" aria-label="Menu" icon="menu" />

        <q-btn flat no-caps no-wrap class="q-ml-xs" v-if="$q.screen.gt.xs">
          <!-- <q-icon name="credit_card" color="red" size="28px" /> -->
          <q-toolbar-title shrink class="text-cyan-4">
            Dashboard
          </q-toolbar-title>
        </q-btn>

        <q-space />

        <div class="q-gutter-sm row items-center no-wrap">

          <q-btn round dense flat color="grey-8" icon="apps" v-if="$q.screen.gt.sm">
            <q-tooltip>Apps</q-tooltip>
          </q-btn>

          <q-btn round dense flat color="grey-8" icon="notifications">
            <q-badge color="red" text-color="white" floating>
              0
            </q-badge>
            <q-tooltip>Notifications</q-tooltip>
          </q-btn>
          <q-btn-dropdown color="primary" flat>
            <template v-slot:label>
              <q-avatar size="30px">
                <q-icon name="account_circle" />
              </q-avatar>
            </template>
            <div class="row no-wrap q-pa-md">
              <div class="column">
                <div class="text-h6 q-mb-md">Settings</div>
                <q-list>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Profile</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Paramètre</q-item-label>
                </q-item-section>
              </q-item>

            </q-list>
              </div>

              <q-separator vertical inset class="q-mx-lg" />

              <div class="column items-center">
                <q-avatar size="72px">
                  <img src="https://cdn.quasar.dev/img/boy-avatar.png">
                </q-avatar>

                <div class="text-subtitle1 q-mt-md q-mb-xs">John Doe</div>

                <q-btn color="primary" label="Deconnexion" push size="sm" v-close-popup  @click="onLogout" />
              </div>
            </div>

          </q-btn-dropdown>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered class=" sidebar text-dark " :width="275"
      :breakpoint="600"  >
      <div class="absolute-top img-header ">
        <div style="display: flex; align-items: center;">
          <q-avatar size="50px" style="margin-right: 8px;">
            <q-img :src="logo" cover />
          </q-avatar>
          <div>
            <span class="text-h6 text-primary">Iziway</span>
          </div>
        </div>
      </div>
      <q-scroll-area class="fit scrollarea" :thumb-style="scrollStyles.thumbStyle" :bar-style="scrollStyles.barStyle">
        <q-list padding class="q-px-sm" style="padding-bottom: 80px !important;">
          <template v-for="section in menuSections" :key="section.title">
            <MenuSection :title="section.title" :links="section.links" />
          </template>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container class="main">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<style>
.app-header {
  background-color: white;
}

.main {
  background-color: #F3F4F6;
  /* overflow: hidden; */
}

.scrollarea {
  height: calc(100vh - 170px);
  margin-top: 70px;
  border-right: none;
}

.sidebar {
  background: #fcfcfa;
  /* background: linear-gradient(90deg, rgba(255, 255, 255, 1) 10%, rgba(102, 204, 153, 1) 90%); */
  height: 100%;
  display: flex;
  flex-direction: column;
  color: white;
  border: none;
  position: relative;
}

.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 2;
}

.absolute-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.img-header {
  width: 96%;
  height: 70px;
  display: flex;
  margin-left: 10px;
  align-items: start;
  justify-content: center;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  opacity: 0.7;
  height: 7px;
  width: 7px;
  background: rgb(182, 157, 157);
}

</style>
