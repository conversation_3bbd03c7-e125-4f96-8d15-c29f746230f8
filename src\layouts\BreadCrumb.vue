<script lang="ts">
  import { defineComponent, ref } from 'vue';

  interface Bread{
    pageTitle?: string;
    subTitle?: string;
    href?: string;
  }

  export default defineComponent({
    name: "BreadCrumb",
    props: {
      bread: {
        type: Object as () => Bread,
        default: () => ({ pageTitle: "", subTitle: "" }),
      },
    },
    setup(props){
      const pageTitle = ref(props.bread.pageTitle);
      const subTitle = ref(props.bread.subTitle);
      const href = ref(props.bread.href);

      return {
        pageTitle, subTitle,href
      };

    }
  })
</script>
<template>
  <q-toolbar class="bg-white q-pa-xs q-ma-xs q-mb-md q-mt-md" style="border-radius: 12px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
    <q-breadcrumbs separator=">" active-color="primary" class="text-grey-8">
      <q-breadcrumbs-el label="Accueil" icon="home" class="hover-primary" to="/" />
      <q-breadcrumbs-el :label="pageTitle" :to="href" class="hover-primary" v-if="pageTitle" />
      <q-breadcrumbs-el :label="subTitle" class="text-weight-medium" v-if="subTitle" />
    </q-breadcrumbs>
  </q-toolbar>
</template>
