function setCookie(c_name: string, c_value: string) {

  var d = new Date();
  d.setTime(d.getTime() + (1 * 24 * 60 * 60 * 1000));
  var expires = "expires=" + d.toUTCString();
  document.cookie = c_name + "=" + c_value + ";" + expires + ";path=/";
}

/**
 * getCookie function
 * Permet de retourner la valeur d'un cookie
 * @param {string} cname
 * @returns value
 */
function getCookie(cname: string) {
  let name = cname + "=";
  let decodedCookie = decodeURIComponent(document.cookie);
  let ca = decodedCookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) == ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
}

function getToken(): string | null {
  // @ts-ignore
  const token = JSON.parse(localStorage.getItem('token'));
  if (typeof token === 'string') {
    return token;
  } else {
    return null;
  }
}

function checkToken() {
  // @ts-ignore
  const expire_date = JSON.parse(localStorage.getItem('tokenExpiration')) as string;
  const tokenExpiration = convertToTime(expire_date);
  let is_auth = true;
  const now = new Date().getTime();
  if (now - tokenExpiration >= 8 * 60 * 60 * 1000) {
    localStorage.removeItem('token');
    localStorage.removeItem('tokenExpiration');
    console.log('Le jeton d\'authentification a été supprimé.');
    is_auth = false;
  }
  return is_auth;
}

function convertToTime(dateString: string) {
  const dateObj = new Date(dateString);
  return dateObj.getTime();
}


function addHours(date: { getTime: () => number; }, hours: number) {
  return new Date(date.getTime() + hours * 60 * 60 * 1000);
}

function getExpires() {
  var d = new Date();

  d.setTime(d.getTime() + (1 * 24 * 60 * 60 * 1000));
  var expires = "expires=" + d.toUTCString();
  return expires;
}

/**
 *deleteCookie function
 * Permet de supprimer un cookie
 * @param {string} cname
 */
function deleteCookie(cname: string) {
  document.cookie = cname + '=; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

function calculateTimeDifference(startTime: Date, endTime: Date) {
  // Convertir les heures de départ et d'arrivée en nombres de minutes
  const startMinutes = startTime.getHours() * 60 + startTime.getMinutes();
  const endMinutes = endTime.getHours() * 60 + endTime.getMinutes();
  // Calculer la différence en nombres de minutes
  let timeDifference = endMinutes - startMinutes;
  // Si la différence est négative, cela signifie que l'heure de fin est le lendemain
  // Donc on ajoute 1440 (nombre de minutes dans une journée) pour obtenir la différence correcte
  if (timeDifference < 0) {
    timeDifference += 1440;
  }
  // Convertir la différence en heures et minutes et retourner le résultat
  const hours = Math.floor(timeDifference / 60);
  let minutes = timeDifference % 60;
  if (minutes == 0) {
    minutes = hours * 60;
  }
  if (hours > 0) {
    minutes += hours * 60;
  }
  return { hours, minutes };
}

const random = (length = 8) => {
  // Declare all characters
  let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  // Pick characers randomly
  let str = '';
  for (let i = 0; i < length; i++) {
    str += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return str;

};

export {
  setCookie, getCookie, getExpires, deleteCookie, calculateTimeDifference, random, getToken, checkToken,
  convertToTime
};
