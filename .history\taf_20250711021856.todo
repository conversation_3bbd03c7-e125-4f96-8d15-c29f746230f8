interface CookieOptions {
  expires?: Date | number;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

// Stockage en mémoire via closure
const createTokenManager = () => {
  let memoryToken: string | null = null;

  return {
    setToken(token: string, rememberMe: boolean = false): void {
      setSecureCookie('auth_token', token, {
        expires: rememberMe ? 24 * 7 : 8,
        secure: true,
        sameSite: 'Strict'
      });
      memoryToken = token;
    },

    getToken(): string | null {
      return getCookie('auth_token') || memoryToken;
    },

    checkToken(): boolean {
      const token = this.getToken();
      if (!token) return false;

      if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(token)) {
        this.clearToken();
        return false;
      }

      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        if (payload.exp && payload.exp * 1000 < Date.now()) {
          this.clearToken();
          return false;
        }
        return true;
      } catch {
        this.clearToken();
        return false;
      }
    },

    clearToken(): void {
      deleteCookie('auth_token');
      memoryToken = null;
    }
  };
};

export const tokenManager = createTokenManager();

// ... (le reste de vos fonctions inchangé)
