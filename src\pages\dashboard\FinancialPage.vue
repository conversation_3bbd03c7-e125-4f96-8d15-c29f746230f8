<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import BreadCrumb from 'src/layouts/BreadCrumb.vue';
import { get_amount_format } from 'src/helpers/utils';

export default defineComponent({
  name: "FinancialPage",
  components: {
    BreadCrumb
  },
  setup() {

    const bread = ref({
      pageTitle: "Santé Financière",
      subTitle: "État de la santé financière de l'entreprise"
    });

    const loading = ref(false);

    const store = adminStore();
    const { financialHealth } = storeToRefs(adminStore());
    const { getFinancialHealth } = store;

    // Données par défaut pour le développement
    const defaultFinancialData = ref({
      financial_data: {
        cash_available: 2500000,
        financial_commitments: 1800000,
        gross_revenue: 5000000,
        net_revenue: 3200000,
        gross_margin: 64,
        liquidity_ratio: 0.72
      },
      health_status: {
        status: "warning",
        messages: [
          "Votre ratio de liquidité est élevé. Surveillez vos engagements."
        ],
        recommendations: [
          "Renégocier les termes avec les fournisseurs",
          "Optimiser la rotation des cotisations"
        ]
      },
      last_updated: "2023-11-15 14:30:00"
    });

    // Données financières principales
    const financialStats = computed(() => [
      {
        title: 'Trésorerie Disponible',
        value: get_amount_format(financialHealth.value?.financial_data?.cash_available || defaultFinancialData.value.financial_data.cash_available),
        icon: 'account_balance_wallet',
        color: 'positive',
        bgColor: 'positive-1',
        trend: '+12%'
      },
      {
        title: 'Engagements Financiers',
        value: get_amount_format(financialHealth.value?.financial_data?.financial_commitments || defaultFinancialData.value.financial_data.financial_commitments),
        icon: 'credit_card',
        color: 'warning',
        bgColor: 'warning-1',
        trend: '-5%'
      },
      {
        title: 'Chiffre d\'Affaires Brut',
        value: get_amount_format(financialHealth.value?.financial_data?.gross_revenue || defaultFinancialData.value.financial_data.gross_revenue),
        icon: 'trending_up',
        color: 'primary',
        bgColor: 'primary-1',
        trend: '+18%'
      },
      {
        title: 'Chiffre d\'Affaires Net',
        value: get_amount_format(financialHealth.value?.financial_data?.net_revenue || defaultFinancialData.value.financial_data.net_revenue),
        icon: 'monetization_on',
        color: 'secondary',
        bgColor: 'secondary-1',
        trend: '+15%'
      }
    ]);

    // Indicateurs de performance
    const performanceIndicators = computed(() => [
      {
        title: 'Marge Brute',
        value: `${(financialHealth.value?.financial_data?.gross_margin || defaultFinancialData.value.financial_data.gross_margin).toFixed(2)}%`,
        icon: 'percent',
        color: 'info',
        bgColor: 'info-1',
        status: 'good'
      },
      {
        title: 'Ratio de Liquidité',
        value: (financialHealth.value?.financial_data?.liquidity_ratio || defaultFinancialData.value.financial_data.liquidity_ratio).toFixed(2),
        icon: 'water_drop',
        color: 'orange',
        bgColor: 'orange-1',
        status: 'warning'
      }
    ]);

    // Statut de santé financière
    const healthStatus = computed(() => {
      const status = financialHealth.value?.health_status?.status || defaultFinancialData.value.health_status.status;
      const statusConfig = {
        good: { color: 'positive', icon: 'check_circle', label: 'Excellente' },
        warning: { color: 'warning', icon: 'warning', label: 'Attention' },
        danger: { color: 'negative', icon: 'error', label: 'Critique' }
      };
      return statusConfig[status as keyof typeof statusConfig] || statusConfig.warning;
    });

    // État pour afficher/masquer la documentation
    const showDocumentation = ref(false);

    const fetchData = async () => {
      loading.value = true;
      const res = await getFinancialHealth();
      if (res.success) {
        financialHealth.value = res.result;
      }
      loading.value = false;
    }

    onMounted(async () => {
      await fetchData();
    });

    return {
      bread, financialHealth, loading, financialStats, performanceIndicators,
      healthStatus, defaultFinancialData, get_amount_format, fetchData, showDocumentation
    }

  },
})
</script>

<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />

    <!-- En-tête avec statut de santé financière -->
    <q-card class="q-mb-lg q-pa-md" flat>
      <div class="row items-center justify-between">
        <div class="col-auto">
          <div class="row items-center q-gutter-md">
            <q-avatar :color="healthStatus.color" text-color="white" size="60px">
              <q-icon :name="healthStatus.icon" size="30px" />
            </q-avatar>
            <div>
              <h5 class="text-h5 q-ma-none text-weight-medium text-grey-8">Santé Financière</h5>
              <div class="text-subtitle1" :class="`text-${healthStatus.color}`">
                {{ healthStatus.label }}
              </div>
            </div>
          </div>
        </div>
        <div class="col-auto">
          <div class="row q-gutter-sm">
            <q-btn
              @click="showDocumentation = !showDocumentation"
              :color="showDocumentation ? 'secondary' : 'grey-6'"
              :icon="showDocumentation ? 'menu_book' : 'help_outline'"
              :label="showDocumentation ? 'Masquer l\'aide' : 'Documentation'"
              unelevated
              outline
            />
            <q-btn
              @click="fetchData"
              :loading="loading"
              color="primary"
              icon="refresh"
              label="Actualiser"
              unelevated
            />
          </div>
        </div>
      </div>
    </q-card>

    <!-- Section Documentation (masquée par défaut) -->
    <q-slide-transition>
      <q-card v-show="showDocumentation" class="q-mb-lg q-pa-md documentation-card" flat bordered>
        <div class="row items-center q-mb-md">
          <q-icon name="menu_book" color="secondary" size="24px" class="q-mr-sm" />
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Documentation des États Financiers</h5>
        </div>

        <div class="row q-col-gutter-md">
          <!-- Colonne 1: Indicateurs de base -->
          <div class="col-md-6 col-sm-12">
            <q-expansion-item
              icon="account_balance_wallet"
              label="Trésorerie Disponible"
              header-class="text-weight-medium"
              default-opened
            >
              <q-card class="q-ma-sm" flat bordered>
                <q-card-section class="q-pa-md">
                  <div class="text-body2 q-mb-sm">
                    <strong>Définition :</strong> Montant total des fonds actuellement disponibles provenant des cotisations non finalisées.
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Explication :</strong> Représente l'argent collecté mais pas encore définitivement acquis.
                    Inclut les cotisations avec statuts : <code>started</code>, <code>in_progress</code>, <code>pending</code>
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Exemple :</strong> Si un client a payé 3 tranches sur 10 mais n'a pas encore terminé, ces fonds sont comptabilisés ici.
                  </div>
                  <q-banner class="bg-info-1 text-info q-mt-sm" rounded>
                    <template v-slot:avatar>
                      <q-icon name="info" />
                    </template>
                    Fonds à utiliser avec précaution (peuvent être remboursés si annulation)
                  </q-banner>
                </q-card-section>
              </q-card>
            </q-expansion-item>

            <q-expansion-item
              icon="credit_card"
              label="Engagements Financiers"
              header-class="text-weight-medium"
            >
              <q-card class="q-ma-sm" flat bordered>
                <q-card-section class="q-pa-md">
                  <div class="text-body2 q-mb-sm">
                    <strong>Définition :</strong> Coût total que l'entreprise doit honorer pour les carnets déjà livrés.
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Calcul :</strong> 80% de la valeur des carnets livrés (hypothèse de marge de 20%)
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Exemple :</strong> Pour un carnet livré à 100.000 FCFA, l'engagement est de 80.000 FCFA
                  </div>
                  <q-banner class="bg-warning-1 text-warning q-mt-sm" rounded>
                    <template v-slot:avatar>
                      <q-icon name="warning" />
                    </template>
                    Impacte directement la trésorerie future
                  </q-banner>
                </q-card-section>
              </q-card>
            </q-expansion-item>

            <q-expansion-item
              icon="trending_up"
              label="Chiffre d'Affaires Brut"
              header-class="text-weight-medium"
            >
              <q-card class="q-ma-sm" flat bordered>
                <q-card-section class="q-pa-md">
                  <div class="text-body2 q-mb-sm">
                    <strong>Définition :</strong> Revenu total généré par les cotisations terminées.
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Formule :</strong> <code>Somme des total_amount WHERE status = 'finished'</code>
                  </div>
                  <div class="text-body2">
                    Ne tient pas compte des coûts associés.
                  </div>
                </q-card-section>
              </q-card>
            </q-expansion-item>
          </div>

          <!-- Colonne 2: Indicateurs avancés -->
          <div class="col-md-6 col-sm-12">
            <q-expansion-item
              icon="monetization_on"
              label="Chiffre d'Affaires Net"
              header-class="text-weight-medium"
            >
              <q-card class="q-ma-sm" flat bordered>
                <q-card-section class="q-pa-md">
                  <div class="text-body2 q-mb-sm">
                    <strong>Définition :</strong> Revenu réel après déduction des coûts des carnets livrés.
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Formule :</strong> <code>CA Brut - Engagements Financiers</code>
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Exemple :</strong> CA Brut de 1.000.000 FCFA - 400.000 FCFA d'engagements = 600.000 FCFA net
                  </div>
                  <q-banner class="bg-positive-1 text-positive q-mt-sm" rounded>
                    <template v-slot:avatar>
                      <q-icon name="check_circle" />
                    </template>
                    Base pour calculer les taxes et bénéfices
                  </q-banner>
                </q-card-section>
              </q-card>
            </q-expansion-item>

            <q-expansion-item
              icon="percent"
              label="Marge Brute"
              header-class="text-weight-medium"
            >
              <q-card class="q-ma-sm" flat bordered>
                <q-card-section class="q-pa-md">
                  <div class="text-body2 q-mb-sm">
                    <strong>Définition :</strong> Pourcentage de profit après coût des carnets livrés.
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Formule :</strong> <code>(CA Net / CA Brut) × 100</code>
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Seuils d'évaluation :</strong>
                  </div>
                  <q-list dense>
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="trending_up" color="positive" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-positive">&gt; 30% : Excellente</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="remove" color="warning" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-warning">20-30% : Satisfaisante</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="trending_down" color="negative" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-negative">&lt; 20% : Dangereuse </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-card-section>
              </q-card>
            </q-expansion-item>

            <q-expansion-item
              icon="water_drop"
              label="Ratio de Liquidité"
              header-class="text-weight-medium"
            >
              <q-card class="q-ma-sm" flat bordered>
                <q-card-section class="q-pa-md">
                  <div class="text-body2 q-mb-sm">
                    <strong>Définition :</strong> Capacité à couvrir les engagements avec la trésorerie disponible.
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Formule :</strong> <code>Engagements / Trésorerie Disponible</code>
                  </div>
                  <div class="text-body2 q-mb-sm">
                    <strong>Interprétation :</strong>
                  </div>
                  <q-list dense>
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="check_circle" color="positive" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-positive">&lt; 0.5 : Situation confortable</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="warning" color="warning" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-warning">0.5-1 : Vigilance requise</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="error" color="negative" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-negative">&gt; 1 : Risque de défaut</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-card-section>
              </q-card>
            </q-expansion-item>
          </div>
        </div>

        <!-- Section Flux Financier -->
        <q-separator class="q-my-lg" />
        <div class="row items-center q-mb-md">
          <q-icon name="account_tree" color="info" size="24px" class="q-mr-sm" />
          <h6 class="text-h6 q-ma-none text-weight-medium text-grey-8">Flux Financier</h6>
        </div>
        <q-card class="q-pa-md bg-grey-1" flat>
          <div class="text-center">
            <div class="flow-diagram">
              <div class="flow-item">
                <q-chip color="primary" text-color="white" icon="account_balance_wallet">
                  Trésorerie
                </q-chip>
                <q-icon name="arrow_forward" class="q-mx-sm" />
              </div>
              <div class="flow-item">
                <q-chip color="secondary" text-color="white" icon="trending_up">
                  CA Brut
                </q-chip>
                <q-icon name="arrow_forward" class="q-mx-sm" />
              </div>
              <div class="flow-item">
                <q-chip color="warning" text-color="white" icon="credit_card">
                  Engagements
                </q-chip>
                <q-icon name="arrow_forward" class="q-mx-sm" />
              </div>
              <div class="flow-item">
                <q-chip color="positive" text-color="white" icon="monetization_on">
                  CA Net
                </q-chip>
              </div>
            </div>
          </div>
        </q-card>
      </q-card>
    </q-slide-transition>

    <!-- Section Indicateurs Financiers Principaux -->
    <q-card class="q-mb-lg q-pa-md" flat>
      <div class="row items-center q-mb-md">
        <div class="col">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Indicateurs Financiers</h5>
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div v-for="(stat, index) in financialStats" :key="index" class="col-md-3 col-sm-6 col-xs-12">
          <q-skeleton height="140px" v-if="loading" />
          <q-card class="financial-card q-pa-md" flat bordered v-else>
            <div class="row items-center q-mb-md">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ stat.value }}</div>
                <div class="text-caption" :class="`text-${stat.color}`">
                  <q-icon name="trending_up" size="14px" />
                  {{ stat.trend }}
                </div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </q-card>

    <!-- Section Ratios et Performance -->
    <q-card class="q-mb-lg q-pa-md" flat>
      <div class="row items-center q-mb-md">
        <div class="col">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Ratios de Performance</h5>
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div v-for="(indicator, index) in performanceIndicators" :key="index" class="col-md-6 col-sm-12">
          <q-skeleton height="120px" v-if="loading" />
          <q-card class="performance-card q-pa-md" flat bordered v-else>
            <div class="row items-center">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ indicator.title }}</div>
                <div class="text-h4 text-weight-bold" :class="`text-${indicator.color}`">{{ indicator.value }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="indicator.bgColor" :text-color="indicator.color" size="60px">
                  <q-icon :name="indicator.icon" size="30px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${indicator.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </q-card>

    <!-- Section Messages et Recommandations -->
    <div class="row q-col-gutter-md">
      <!-- Messages d'alerte -->
      <div class="col-md-6 col-sm-12">
        <q-card class="q-pa-md" flat bordered>
          <div class="row items-center q-mb-md">
            <q-icon name="info" color="info" size="24px" class="q-mr-sm" />
            <h6 class="text-h6 q-ma-none text-weight-medium text-grey-8">Messages d'Information</h6>
          </div>

          <div v-if="loading" class="q-gutter-sm">
            <q-skeleton type="text" />
            <q-skeleton type="text" width="80%" />
          </div>
          <div v-else>
            <q-list separator>
              <q-item
                v-for="(message, index) in (financialHealth?.health_status?.messages || defaultFinancialData.health_status.messages)"
                :key="index"
                class="q-px-none"
              >
                <q-item-section avatar>
                  <q-avatar color="info-1" text-color="info" size="32px">
                    <q-icon name="info" size="16px" />
                  </q-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-body2">{{ message }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card>
      </div>

      <!-- Recommandations -->
      <div class="col-md-6 col-sm-12">
        <q-card class="q-pa-md" flat bordered>
          <div class="row items-center q-mb-md">
            <q-icon name="lightbulb" color="warning" size="24px" class="q-mr-sm" />
            <h6 class="text-h6 q-ma-none text-weight-medium text-grey-8">Recommandations</h6>
          </div>

          <div v-if="loading" class="q-gutter-sm">
            <q-skeleton type="text" />
            <q-skeleton type="text" width="90%" />
            <q-skeleton type="text" width="70%" />
          </div>
          <div v-else>
            <q-list separator>
              <q-item
                v-for="(recommendation, index) in (financialHealth?.health_status?.recommendations || defaultFinancialData.health_status.recommendations)"
                :key="index"
                class="q-px-none"
              >
                <q-item-section avatar>
                  <q-avatar color="warning-1" text-color="warning" size="32px">
                    <q-icon name="lightbulb" size="16px" />
                  </q-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-body2">{{ recommendation }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card>
      </div>
    </div>

    <!-- Footer avec dernière mise à jour -->
    <div class="q-mt-lg text-center">
      <div class="text-caption text-grey-6">
        Dernière mise à jour : {{ financialHealth?.last_updated || defaultFinancialData.last_updated }}
      </div>
    </div>

  </q-page>
</template>

<style scoped>
.financial-card, .performance-card {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.financial-card:hover, .performance-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 12px 12px;
}

.q-card {
  border-radius: 12px;
}

.q-avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Animation pour les indicateurs */
.financial-card .text-h5,
.performance-card .text-h4 {
  transition: all 0.3s ease;
}

.financial-card:hover .text-h5,
.performance-card:hover .text-h4 {
  transform: scale(1.05);
}

/* Style pour les listes */
.q-list .q-item {
  border-radius: 8px;
  margin-bottom: 8px;
}

.q-list .q-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Documentation styles */
.documentation-card {
  border: 2px dashed #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.documentation-card .q-expansion-item {
  border-radius: 8px;
  margin-bottom: 8px;
  background: white;
}

.documentation-card .q-expansion-item__header {
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
}

.documentation-card code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  color: #d63384;
}

.flow-diagram {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.flow-item {
  display: flex;
  align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .financial-card, .performance-card {
    margin-bottom: 16px;
  }

  .text-h4 {
    font-size: 1.8rem;
  }

  .text-h5 {
    font-size: 1.4rem;
  }

  .flow-diagram {
    flex-direction: column;
  }

  .flow-item .q-icon {
    transform: rotate(90deg);
  }

  .documentation-card {
    margin: 8px 0;
  }
}
</style>

