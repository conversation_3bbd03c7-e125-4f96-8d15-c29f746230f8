<script lang="ts">
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent,ref,onMounted } from 'vue';

interface Collector {
  id: number
  last_name: string
  first_name: string
  phone: string
  email: string | null
  total_subscriptions: number
  total_revenue: number
  average_sale_amount: number
  last_subscription_date: string
}

export default defineComponent({
  name: 'BestSellingCollector',
  setup() {

    const collectors = ref<Collector[]>([]);
    const loading = ref(false);
    const store = adminStore();

    const getCollectors = async () => {
      loading.value = true;
      try {
        const response = await store.getTopCollectors({year:2025});
        console.log("best collectors",response);
        if (response.success) {
          collectors.value = response.result;
        }

      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    };

    // Fonction pour formater le montant en FCFA
    const formatRevenue = (amount: number): string => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XOF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount)
    }

    onMounted(() => {
      getCollectors();
    });

    return {
      collectors,
      loading,
      formatRevenue
    };
  }
});
</script>

<template>
  <q-card class="q-pa-md" flat style="height: 420px;">
    <div class="row items-center q-mb-md">
      <div class="col">
        <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Meilleurs Collecteurs</h5>
      </div>
    </div> <!-- Liste des meilleurs collecteurs -->
    <q-scroll-area style="height: 300px;">
      <q-spinner v-if="loading" color="primary" size="3em" />
      <div v-if="!loading && collectors.length > 0" class="q-gutter-sm">
        <div v-for="collector in collectors" :key="collector.id" class="row items-center q-py-sm">
          <div class="col-auto">
            <q-avatar size="40px" color="grey-3">
              <q-icon name="person" color="grey-6" />
            </q-avatar>
          </div>
          <div class="col q-ml-sm">
            <div class="text-body2 text-weight-medium">{{ collector.first_name }} {{ collector.last_name }}</div>
            <div class="text-caption text-grey-6">{{ collector.total_subscriptions }} carnets vendus</div>
          </div>
          <div class="col-auto">
            <div class="text-body2 text-weight-bold text-blue-6">{{ formatRevenue(collector.total_revenue) }}</div>
          </div>
        </div>
      </div>
    </q-scroll-area>
    <!-- Message si pas de données -->
    <div v-if="!loading && collectors.length === 0" class="text-center text-grey-5 q-mt-xl">
      <q-icon name="star" size="48px" class="q-mb-md" />
      <div>Aucun collecteur actif</div>
    </div>
  </q-card>
</template>


