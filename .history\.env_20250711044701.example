# Configuration de l'API
VITE_API_BASE_URL=https://api-iziway.benilife.xyz/api

# Clés de sécurité (OBLIGATOIRE en production)
# Générez des clés sécurisées d'au moins 32 caractères
VITE_ENCRYPTION_KEY=your-secure-encryption-key-here-32-chars-minimum
VITE_SALT=your-secure-salt-key-here-32-chars-minimum

# Mode de développement
NODE_ENV=development

# Exemple de clés sécurisées pour le développement local
# VITE_ENCRYPTION_KEY=iziway-dev-encryption-key-2024-secure-development-mode
# VITE_SALT=iziway-dev-salt-key-2024-secure-development-mode-salt

# Instructions pour générer des clés sécurisées :
# 1. Utilisez un générateur de mots de passe sécurisé
# 2. Assurez-vous que chaque clé fait au moins 32 caractères
# 3. Utilisez des caractères alphanumériques et des symboles
# 4. Ne partagez jamais ces clés dans le code source
