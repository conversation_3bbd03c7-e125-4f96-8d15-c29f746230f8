<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import ProductTable from 'src/components/tables/ProductTable.vue';
  import AddProductModal from 'src/components/modals/AddProductModal.vue';
  export default defineComponent({
    name: "ProductsPage",
    components: {
      BreadCrumb,ProductTable,AddProductModal
    },
    setup(){

      const bread = ref({
        pageTitle: "Produits",
        subTitle: "Gestion des produits"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat >
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion des Produits
            </q-toolbar-title>
            <AddProductModal class="q-mr-xs" />
          </q-toolbar>
          <ProductTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
