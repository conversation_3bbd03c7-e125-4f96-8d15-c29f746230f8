// src/utils/env.ts
import { z } from 'zod'

const envSchema = z.object({
  API_URL: z.url(),
  ENCRYPTION_KEY: z.string().min(32),
  MODE: z.enum(['development', 'production']),
  DEV: z.coerce.boolean(),
})

export const env = envSchema.parse({
  API_URL: process.env.API_URL || import.meta.env.API_URL,
  ENCRYPTION_KEY: process.env.ENCRYPTION_KEY,
  MODE: process.env.NODE_ENV,
  DEV: import.meta.env.DEV,
})
