<script lang="ts">
  import CityTable from 'src/components/tables/CityTable.vue';
  import { defineComponent, ref } from 'vue';
  export default defineComponent({
    name: "CityPage",
    components: { CityTable,  },
    setup() {
      const bread = ref({
        pageTitle: "Villes",
        subTitle: "Gestion des villes"
      });

      return {
        bread
      }
    },
})
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Gestion des villes
            </q-toolbar-title>
            <!-- <AddCategoryModal class="q-mr-xs" /> -->
          </q-toolbar>
          <CityTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
