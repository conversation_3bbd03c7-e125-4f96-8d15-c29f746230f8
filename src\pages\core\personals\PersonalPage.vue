<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import PersonalTable from 'src/components/tables/PersonalTable.vue';
  import AddPersonalModal from 'src/components/modals/AddPersonalModal.vue';
  import AffectPersonalModal from 'src/components/modals/AffectPersonalModal.vue';

  export default defineComponent({
    name: "PersonalPage",
    components: {
      BreadCrumb,PersonalTable,AddPersonalModal,AffectPersonalModal
    },
    setup(){
      const bread = ref({
        pageTitle: "Personnels",
        subTitle: "Gestion Ressources Humaines"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat >
          <q-toolbar class="text-dark">
            <q-toolbar-title>
              Gestion des Personnels
            </q-toolbar-title>
            
            <AddPersonalModal />
          </q-toolbar>
          <PersonalTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
