<script lang="ts">
import { defineComponent, ref, toRefs, PropType, onMounted } from 'vue';
import { get_amount_format, get_date_format } from 'src/helpers/utils';
import { Subscription } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';

interface InactifCarnet {
  carnet: Subscription;
  date_diff: number;
  last_payment_date: number;
}
export default defineComponent({
  name: 'ListCarnetInactif',

  setup() {
    const loading = ref(true);
    const carnets = ref<InactifCarnet[]>([]);
    const initialPagination = ref({
      sortBy: 'name',
      descending: true,
      page: 1,
      rowsPerPage: 10
    });
    const filter = ref("");

    const headers = [
      { name: 'id', label: 'ID', field: 'id', sortable: true, align: "left" },
      { name: 'agency', label: 'AGENCE ', field: 'agency', sortable: true, align: "left" },
      { name: 'client', label: 'CLIENT', field: 'client', sortable: true, align: "left" },
      { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true, align: "left" },
      { name: 'subscription', label: 'CARNET(PACK)', field: 'subscription', sortable: true, align: "left" },
      { name: 'cotisation', label: 'MONTANT COTISER', field: 'cotisation', sortable: true, align: "left" },
      { name: 'rest_cotise', label: 'RESTE A COTISER', field: 'rest_cotise', sortable: true, align: "left" },
      { name: 'percentage', label: 'POURCENTAGE', field: 'percentage', sortable: true, align: "left" },
      { name: 'period', label: 'PERIODE', field: 'period', sortable: true, align: "left" },
      { name: 'last_payment_date', label: 'DERNIER PAIEMENT', field: 'last_payment_date', sortable: true, align: "left" },
      { name: 'finished_at', label: 'DATE FIN', field: 'finished_at', sortable: true, align: "left" },
    ] as any;

    const columns_visibles = [
      'code', 'agency', 'client', 'collector', 'subscription', 'cotisation', 'period', 'payment_date', 'last_payment_date',
      'rest_cotise', 'percentage'
    ];

    const store = adminStore();
    const { getCarnetInactif } = store;

    onMounted(async () => {
      const res = await getCarnetInactif();
      // console.log("carnets inactifs", res);
      if (res.message) {
        loading.value = false;

      }
      if (res.success) {
        carnets.value = res.result;
      } else {
        console.log("error", res);
      }
    })

    return {
      headers, get_amount_format, initialPagination, filter, columns_visibles, get_date_format, carnets, loading,
    }
  }
});

</script>
<template>
  <div class="q-pa-md">
    <q-table flat bordered title="Liste des carnets inactifs" :rows="carnets" :columns="headers" row-key="name"
      :pagination="initialPagination" :filter="filter" :loading="loading" table-style="max-width: 100%;"
      :visible-columns="columns_visibles">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="agency" :props="props">
            {{ props.row.carnet.agency?.name }}
          </q-td>
          <q-td key="client" :props="props">
            {{ props.row.carnet.client?.nom }} {{ props.row.carnet.client?.prenoms }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.carnet.collector?.nom }} {{ props.row.carnet.collector?.prenoms }}
          </q-td>
          <q-td key="subscription" :props="props">
            {{ props.row.carnet?.code }} ({{ props.row.carnet?.pack?.name }})
          </q-td>
          <q-td key="cotisation" :props="props">
            {{ get_amount_format(props.row.carnet?.cotisation?.total_amount) }}
          </q-td>
          <q-td key="rest_cotise" :props="props">
            {{ get_amount_format(props.row.carnet?.pack?.total_price - props.row.carnet?.cotisation?.total_amount) }}
          </q-td>
          <q-td key="percentage" :props="props">

            <q-knob show-value font-size="12px" v-model="props.row.percent" size="50px" :thickness="0.22" color="primary"
              track-color="grey-3" class="q-ma-md">
              {{ props.row.percent }}%
            </q-knob>
          </q-td>
          <q-td key="period" :props="props">
            {{ get_date_format(props.row.carnet?.started_at) }} au {{ get_date_format(props.row.carnet?.finished_at) }}
          </q-td>
          <q-td key="last_payment_date" :props="props">
            {{ get_date_format(props.row.last_payment_date) }}
          </q-td>

          <q-td key="payment_mode" :props="props">
            {{ props.row.payment_mode }}
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
