<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import CategoryTable from 'src/components/tables/CategoryTable.vue';
  import AddCategoryModal from 'src/components/modals/AddCategoryModal.vue';
  export default defineComponent({
    name: "CategoryPage",
    components: {
      BreadCrumb,CategoryTable,AddCategoryModal
    },
    setup(){

      const bread = ref({
        pageTitle: "Categories",
        subTitle: "Gestion des Categories"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat >
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion des Categories de Produits
            </q-toolbar-title>
            <AddCategoryModal class="q-mr-xs" />
          </q-toolbar>
          <CategoryTable/>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
