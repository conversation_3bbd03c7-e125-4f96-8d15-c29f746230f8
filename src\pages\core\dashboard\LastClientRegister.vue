<script lang="ts">
  import { defineComponent, ref,PropType,toRefs } from 'vue';
  import { get_date_format,get_amount_format } from 'src/helpers/utils';
  export default defineComponent({
    name: "LastClientRegister",
    props: {
      clients: {
        type: Array as PropType<any[]>,
        required: true,
      }
    },
    setup(props){
      const {clients} = toRefs(props);

      return {
        clients,get_date_format
      }
    }
  })
</script>
<template>
  <div class="q-pa-md" style="max-width: 100%">
    <q-list separator>
      <q-item v-for="(item,i) in clients" :key="i" >
        <q-item-section>
          <div style="display: flex;">
            <q-avatar size="37px" font-size="52px" color="teal" text-color="white" icon="account_circle" />
            <q-item-label>{{ item.nom }} {{ item.prenoms }} </q-item-label>

          </div>
          
        </q-item-section>

        <q-item-section side top>
          <q-item-label caption>{{ get_date_format(item.created_at) }}</q-item-label>
          <!-- <q-icon name="star" color="yellow" /> -->
        </q-item-section>
        <q-separator spaced inset />
      </q-item>

    </q-list>
  </div>
</template>