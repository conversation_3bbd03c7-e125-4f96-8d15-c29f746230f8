<template>
  <q-card class="financial-trend-card" flat>
    <div class="row items-center q-mb-md">
      <div class="col">
        <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Historique financier</h5>
      </div>
      <div class="col-auto">
        <div class="row items-center q-gutter-sm">
          <q-select v-model="selectedYear" :options="yearOptions" label="Année" outlined dense
            style="min-width: 120px;" />
          <q-select v-if="selectedPeriod === 'month'" v-model="selectedMonth" :options="monthOptions" emit-value map-options
            option-value="value" option-label="label" label="Mois" outlined dense style="min-width: 140px;" />
          <q-btn-dropdown color="primary" :label="getPeriodLabel(selectedPeriod)" size="sm" outline no-caps>
            <q-list>
              <q-item v-for="period in periodOptions" :key="period.value" clickable v-close-popup
                @click="selectedPeriod = period.value" :active="selectedPeriod === period.value">
                <q-item-section>
                  <q-item-label>{{ period.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>
      </div>
    </div>

    <!-- Graphique -->
    <div style="height: 320px;">
      <apexchart type="line" height="100%" :options="chartOptions" :series="series" />
    </div>

    <q-inner-loading :showing="loading" />
  </q-card>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import VueApexCharts from 'vue3-apexcharts';
import { api } from 'boot/axios';
import { adminStore } from 'src/stores/core/adminStore';

export default defineComponent({
  name: 'FinancialTrendCard',
  components: {
    apexchart: VueApexCharts,
  },
  setup() {
    // États
    const selectedPeriod = ref('week');
    const selectedYear = ref(new Date().getFullYear());
    const selectedMonth = ref(new Date().getMonth() + 1); // 1-12
    const loading = ref(false);
    const chartData = ref<{
      labels: string[];
      subscriptions: number[];
      cotisations: number[];
      revenus: number[];
    }>({
      labels: [],
      subscriptions: [],
      cotisations: [],
      revenus: []
    });

    // Options
    const periodOptions = [
      { value: 'today', label: "Aujourd'hui" },
      { value: 'week', label: 'Semaine' },
      { value: 'month', label: 'Mois' },
      { value: 'year', label: 'Année' }
    ];

    const yearOptions = computed(() => {
      const currentYear = new Date().getFullYear();
      return Array.from({ length: 5 }, (_, i) => currentYear - i);
    });

    const monthOptions = [
      { value: 1, label: 'Janvier' },
      { value: 2, label: 'Février' },
      { value: 3, label: 'Mars' },
      { value: 4, label: 'Avril' },
      { value: 5, label: 'Mai' },
      { value: 6, label: 'Juin' },
      { value: 7, label: 'Juillet' },
      { value: 8, label: 'Août' },
      { value: 9, label: 'Septembre' },
      { value: 10, label: 'Octobre' },
      { value: 11, label: 'Novembre' },
      { value: 12, label: 'Décembre' }
    ];

    // Méthodes
    const getPeriodLabel = (period: string) => {
      return periodOptions.find(p => p.value === period)?.label || 'Période';
    };

    const store = adminStore();

    const fetchData = async () => {
      loading.value = true;
      try {
        // Simulation de données - À remplacer par un appel API réel
        chartData.value = getSimulatedData(selectedPeriod.value, selectedYear.value, selectedMonth.value);

        // Exemple d'appel API (à décommenter et adapter)

        const response = await store.getFinancialTrends({
          period: selectedPeriod.value,
          year: selectedYear.value
        });
        if (response.success) {
          chartData.value = response.result;
        }

      } catch (error) {
        console.error('Erreur:', error);
      } finally {
        loading.value = false;
      }
    };

    // Simulation de données (à remplacer par des données réelles)
    const getSimulatedData = (period: string, year: number, month?: number) => {
      if (period === 'month' && month) {
        // Générer les données pour tous les jours du mois
        const daysInMonth = new Date(year, month, 0).getDate();
        const labels = Array.from({ length: daysInMonth }, (_, i) => (i + 1).toString());

        // Générer des données aléatoires mais cohérentes pour chaque jour
        const subscriptions = Array.from({ length: daysInMonth }, (_, i) => {
          const base = 8 + Math.sin((i + 1) / 7) * 5; // Variation hebdomadaire
          return Math.max(1, Math.round(base + Math.random() * 6 - 3));
        });

        const cotisations = Array.from({ length: daysInMonth }, (_, i) => {
          const base = 300000 + Math.sin((i + 1) / 7) * 150000;
          return Math.max(50000, Math.round(base + Math.random() * 200000 - 100000));
        });

        const revenus = Array.from({ length: daysInMonth }, (_, i) => {
          const base = 800000 + Math.sin((i + 1) / 7) * 400000;
          return Math.max(200000, Math.round(base + Math.random() * 500000 - 250000));
        });

        return { labels, subscriptions, cotisations, revenus };
      }

      const baseData = {
        today: {
          labels: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00', '24:00'],
          subscriptions: [5, 3, 7, 12, 18, 14, 10, 8, 5],
          cotisations: [150000, 100000, 250000, 400000, 600000, 450000, 350000, 250000, 150000],
          revenus: [250000, 150000, 420000, 750000, 1100000, 850000, 600000, 450000, 300000]
        },
        week: {
          labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
          subscriptions: [15, 12, 18, 22, 14, 20, 25],
          cotisations: [500000, 400000, 600000, 750000, 450000, 700000, 900000],
          revenus: [1200000, 950000, 1500000, 1850000, 1150000, 1700000, 2100000]
        },
        year: {
          labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
          subscriptions: [120, 110, 130, 150, 140, 160, 180, 170, 150, 140, 130, 160],
          cotisations: [4250000, 3750000, 4500000, 5500000, 4750000, 5750000, 6500000, 6000000, 5250000, 4750000, 4250000, 5500000],
          revenus: [8500000, 7800000, 9200000, 11000000, 9500000, 11500000, 13000000, 12000000, 10500000, 9500000, 8500000, 11000000]
        }
      };

      return baseData[period as keyof typeof baseData] || baseData.week;
    };

    // Watchers
    watch([selectedPeriod, selectedYear, selectedMonth], () => {
      fetchData();
    }, { immediate: true });

    // Calculs
    const series = computed(() => [
      {
        name: 'Souscriptions',
        type: 'column',
        data: chartData.value.subscriptions
      },
      {
        name: 'Cotisations (XOF)',
        type: 'area',
        data: chartData.value.cotisations,
        yAxisIndex: 1
      },
      {
        name: 'Revenus (XOF)',
        type: 'line',
        data: chartData.value.revenus,
        yAxisIndex: 1
      }
    ]);

    const chartOptions = computed(() => ({
      chart: {
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: false,
            reset: true
          }
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#1976D2', '#42A5F5', '#66BB6A'],
      stroke: {
        width: [0, 2, 3],
        curve: 'smooth'
      },
      plotOptions: {
        bar: {
          columnWidth: '50%',
          borderRadius: 4
        }
      },
      fill: {
        opacity: [0.85, 0.25, 1]
      },
      labels: chartData.value.labels,
      xaxis: {
        type: 'category',
        labels: {
          style: {
            colors: '#666',
            fontSize: '12px'
          }
        }
      },
      yaxis: [
        {
          title: { text: 'Souscriptions', style: { color: '#666' } },
          labels: { style: { colors: '#666' } }
        },
        {
          opposite: true,
          title: {
            text: 'Montants (XOF)',
            style: { color: '#66BB6A' }
          },
          labels: {
            style: { colors: '#66BB6A' },
            formatter: (val: number) => val.toLocaleString() + ' XOF'
          }
        }
      ],
      tooltip: {
        shared: true,
        y: [
          { formatter: (y: number) => y?.toFixed(0) + ' souscriptions' },
          { formatter: (y: number) => y?.toLocaleString() + ' XOF' },
          { formatter: (y: number) => y?.toLocaleString() + ' XOF' }
        ]
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left'
      },
      grid: {
        borderColor: '#f1f1f1'
      }
    }));

    return {
      selectedPeriod,
      selectedYear,
      selectedMonth,
      periodOptions,
      yearOptions,
      monthOptions,
      loading,
      series,
      chartOptions,
      getPeriodLabel
    };
  }
});
</script>

<style scoped>
.financial-trend-card {
  height: 100%;
  padding: 16px;
}

.financial-trend-card .q-card__section {
  padding: 0;
}
</style>
