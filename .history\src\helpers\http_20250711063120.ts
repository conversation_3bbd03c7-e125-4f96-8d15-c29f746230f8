// src/utils/http.ts
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { Response } from '../models';
import { tokenService } from 'src/services/token.service';
import { env } from 'src/utils/env';

// Configuration Axios optimisée pour éviter les problèmes CORS
console.log('🌐 Configuration HTTP Client avec baseURL:', env.API_URL);
const httpClient = axios.create({
  baseURL: env.API_URL,
  timeout: 30000, // Augmenté à 30 secondes pour debug
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: false // Désactivé pour éviter les conflits CORS avec l'authentification Bearer
});

// Intercepteur pour ajouter le token
httpClient.interceptors.request.use(
  async (config) => {
    try {
      console.log('🚀 Intercepteur déclenché pour:', config.url);
      const token = await tokenService.getToken();
      console.log('🔑 Token récupéré dans l\'intercepteur:', token ? `Token présent (${token.substring(0, 10)}...)` : 'Aucun token');

      if (token) {
        // Ajouter le header Authorization
        config.headers = config.headers || {};
        config.headers['Authorization'] = `Bearer ${token}`;
        console.log('✅ Header Authorization ajouté:', `Bearer ${token.substring(0, 10)}...`);
      } else {
        console.log('❌ Aucun token disponible pour la requête');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du token:', error);
    }

    return config;
  },
  (error) => {
    console.error('❌ Erreur dans l\'intercepteur de requête:', error);
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs
httpClient.interceptors.response.use(
  (response) => {
    console.log('✅ Requête réussie:', response.config.url, 'Status:', response.status);
    return response;
  },
  async (error: AxiosError) => {
    console.error('❌ Erreur API détaillée:');
    console.error('  URL:', error.config?.url);
    console.error('  Méthode:', error.config?.method?.toUpperCase());
    console.error('  Status:', error.response?.status);
    console.error('  Headers envoyés:', error.config?.headers);
    console.error('  Message:', error.message);
    console.error('  Réponse complète:', error.response?.data);

    // TEMPORAIRE: Gestion des 401 désactivée pour debug
    // if (error.response?.status === 401) {
    //   console.log('🚨 Erreur 401 détectée - Nettoyage du token et redirection');
    //   tokenService.clearToken();
    //   window.location.href = '/auth/login';
    // }

    return Promise.reject(normalizeError(error));
  }
);

/**
 * Interface pour les données d'erreur de l'API
 */
interface ApiErrorData {
  message?: string;
  errors?: any;
}

/**
 * Normalise les erreurs Axios
 */
function normalizeError(error: AxiosError): Response {
  console.error('API Error:', error);

  if (error.response) {
    const errorData = error.response.data as ApiErrorData;
    return {
      success: false,
      message: errorData?.message || 'Erreur serveur',
      result: null,
      errors: errorData?.errors || null
    };
  }

  return {
    success: false,
    message: error.message || 'Erreur de connexion',
    result: null,
    errors: 'Erreur réseau'
  };
}

/**
 * Fonctions API sécurisées
 */
export const api = {
  async get(url: string, params?: Record<string, any>): Promise<Response> {
    console.log('📡 API GET appelé:', url, 'avec params:', params);
    try {
      const response = await httpClient.get(url, { params });
      return response.data;
    } catch (error) {
      console.error('❌ Erreur dans api.get pour:', url, error);
      return normalizeError(error as AxiosError);
    }
  },

  async post(url: string, payload: any, config?: AxiosRequestConfig): Promise<Response> {
    console.log('📡 API POST appelé:', url, 'avec payload:', payload);
    try {
      const response = await httpClient.post(url, payload, config);
      return response.data;
    } catch (error) {
      console.error('❌ Erreur dans api.post pour:', url, error);
      return normalizeError(error as AxiosError);
    }
  },

  async postWithFile(url: string, formData: FormData): Promise<Response> {
    try {
      const response = await httpClient.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async put(url: string, payload: any, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.put(url, payload, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async delete(url: string, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.delete(url, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  }
};

/**
 * postDataWithToken function
 * Send HTTP POST request with authorization bearer
 * @param url
 * @param payload
 * @returns response
 */
export const postDataWithToken = async (url: string, payload?: any): Promise<Response> => {
  return api.post(url, payload);
};

/**
 * getDataWithToken function
 * Send HTTP GET request with authorization bearer
 * @param url
 * @param payload - Optional payload for GET requests (will be sent as query params)
 * @returns response
 */
export const getDataWithToken = async (url: string, payload?: any): Promise<Response> => {
  return api.get(url, payload);
};

/**
 * getDataWithParams function
 * Send HTTP GET request with parameters and authorization bearer
 * @param url
 * @param params
 * @returns response
 */
export const getDataWithParams = async (url: string, params?: Record<string, any>): Promise<Response> => {
  return api.get(url, params);
};

/**
 * postDataWithFile function
 * Send HTTP POST request with file upload
 * @param url
 * @param formData
 * @returns response
 */
export const postDataWithFile = async (url: string, formData: FormData): Promise<Response> => {
  return api.postWithFile(url, formData);
};

/**
 * postData function
 * Send HTTP POST request without token (for public endpoints)
 * @param url
 * @param payload
 * @returns response
 */
export const postData = async (url: string, payload?: any): Promise<Response> => {
  try {
    // Create a temporary client without auth for public endpoints
    const response = await axios.post(
      `${env.API_URL}/${url}`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    return normalizeError(error as AxiosError);
  }
};

