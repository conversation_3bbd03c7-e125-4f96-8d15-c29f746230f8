import { defineStore } from 'pinia'
import { postData, postDataWithToken } from '../../helpers/http';
import { getCookie, deleteCookie, convertToTime } from '../../helpers/myfunc';
import { tokenService } from '../../services/token.service';
import { CryptoService } from '../../services/crypto.service';
import type { User } from '../../models';
import type { Response } from '../../models';

// Fonction pour récupérer les données utilisateur cryptées
async function getStoredUserData(): Promise<User | null> {
  try {
    const encryptedUserData = localStorage.getItem('user');
    if (!encryptedUserData) return null;

    const encryptedData = JSON.parse(encryptedUserData);
    const decryptedData = await CryptoService.decrypt(encryptedData);
    return JSON.parse(decryptedData) as User;
  } catch (error) {
    console.error('Erreur lors du décryptage des données utilisateur:', error);
    localStorage.removeItem('user');
    return null;
  }
}

// Fonction pour stocker les données utilisateur cryptées
async function storeUserData(user: User): Promise<void> {
  try {
    const userData = JSON.stringify(user);
    const encryptedData = await CryptoService.encrypt(userData);
    localStorage.setItem('user', JSON.stringify(encryptedData));
  } catch (error) {
    console.error('Erreur lors du cryptage des données utilisateur:', error);
    throw new Error('USER_ENCRYPTION_FAILED');
  }
}

export const authStore = defineStore('auth', {
  // other options...
  state: () => ({
    user: {} as User,
    loading: false,
    error: null,
    token: null as string | null,
    isInitialized: false,
  }),
  getters: {
    getUser: (state) => {
      return state.user;
    },
    getToken: (state) => {
      return state.token;
    },


  },
  actions: {

    async login(payload: any): Promise<Response> {
      try {
        const response = await postData('auth/login', payload);
        if (response.success) {
          const result = response.result;
          const role_id = result.user.role_id;
          if (role_id === 1 || role_id === 2) {
            this.user = result.user;
            this.token = result.token;
            localStorage.setItem('user', JSON.stringify(this.user));
            localStorage.setItem('token', JSON.stringify(this.token));
            const expirationTime = new Date(Date.now() + 8 * 60 * 60 * 1000).toUTCString();
            const tokenExpiration = convertToTime(expirationTime) as any;
            localStorage.setItem('tokenExpiration', tokenExpiration);
            // document.cookie = `token=${this.token}; expires=${expirationTime}`;
            return response;
          } else {
            return {
              success: false,
              message: "Vous n'avez pas le privilège de vous connecter",
              result: null,
              errors: null
            };

          }
        } else {
          return {
            success: false,
            message: response.message,
            result: null,
            errors: response.errors
          };
        }
      } catch (error: any) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: "Erreur de connexion"
        };
      }
    },

    async register_partner(payload: any): Promise<Response> {
      try {
        const response = await postData('auth/partner/register', payload);
        //this.user = response.result;
        return response;

      } catch (error: any) {
        return error;
      }
    },

    async verify_code(payload: any): Promise<Response> {
      const response = await postData('auth/partner/verify-account', payload);
      //this.user = response.result;
      return response;
    },

    async logout(): Promise<Response> {
      const response = await postDataWithToken('auth/logout');
      if (response.success) {
        this.user = {
          id: 0,
          username: '',
          email: '',
          role_id: 0,
          password: '',
        };
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('tokenExpiration');
        // deleteCookie('token');

        return response;
      } else {
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
        }
      }
    },

    async register_user(payload: any): Promise<Response> {
      try {
        const response = await postData('auth/user/register', payload);
        //this.user = response.result;
        return response;

      } catch (error: any) {
        return error;
      }
    }


  }
});
