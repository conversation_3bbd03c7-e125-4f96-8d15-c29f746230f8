import { env } from '../utils/env';

interface EncryptedData {
  iv: string;    // Vecteur d'initialisation
  cipher: string; // Données chiffrées
  authTag: string; // Tag d'authentification (GCM)
}

export class CryptoService {
  private static algorithm = 'AES-GCM';
  private static key: CryptoKey | null = null;

  /**
   * Initialise la clé de chiffrement
   */
  private static async initKey(): Promise<CryptoKey> {
    if (this.key) return this.key;

    const secret = env.ENCRYPTION_KEY;
    if (!secret || secret.length < 32) {
      throw new Error('Invalid encryption secret - Must be 32+ chars');
    }

    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secret),
      'PBKDF2',
      false,
      ['deriveKey']
    );

    // 3. Dérivation avec salt dynamique (stocké côté serveur idéalement)
    const salt = await this.fetchSalt(); // Récupération sécurisée
    this.key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: encoder.encode(salt),
        iterations: 310_000, // OWASP recommandation 2023
        hash: 'SHA-512'
      },
      keyMaterial,
      { name: this.algorithm, length: 256 },
      true, // Extrable pour backup HSM
      ['encrypt', 'decrypt']
    );

    return this.key;
  }

  /**
   * Récupère le salt depuis un endpoint sécurisé
   */
  private static async fetchSalt(): Promise<string> {
    const response = await fetch('/api/auth/encryption-salt', {
      credentials: 'omit',
      cache: 'no-store'
    });

    if (!response.ok) throw new Error('Failed to fetch encryption salt');
    return response.text();
  }

  /**
   * Chiffrement production-grade
   */
  static async encrypt(data: string): Promise<EncryptedData> {
    try {
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const key = await this.initKey();

      const result = await crypto.subtle.encrypt(
        { name: this.algorithm, iv },
        key,
        new TextEncoder().encode(data)
      );

      // Conversion en format sérialisable
      return {
        iv: this.arrayToHex(iv),
        cipher: this.arrayToHex(new Uint8Array(result)),
        authTag: this.arrayToHex(new Uint8Array(result, result.byteLength - 16)) // Tag GCM
      };
    } catch (error) {
      this.auditError(error, 'encrypt');
      throw new Error('ENCRYPTION_FAILED');
    }
  }

  /**
   * Déchiffrement avec vérification d'intégrité
   */
  static async decrypt(encrypted: EncryptedData): Promise<string> {
    try {
      const iv = this.hexToArray(encrypted.iv);
      const cipher = this.hexToArray(encrypted.cipher);
      const key = await this.initKey();

      const decrypted = await crypto.subtle.decrypt(
        {
          name: this.algorithm,
          iv,
          additionalData: new TextEncoder().encode(env.VITE_APP_ID) // Context binding
        },
        key,
        cipher
      );

      return new TextDecoder().decode(decrypted);
    } catch (error) {
      this.auditError(error, 'decrypt');
      throw new Error('DECRYPTION_FAILED');
    }
  }

  // [...] Méthodes utilitaires (hexToArray, arrayToHex, auditError, etc.)

  private static hexToArray(hex: string): Uint8Array {
    const array = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      array[i / 2] = parseInt(hex.slice(i, i + 2), 16);
    }
    return array;
  }

  private static arrayToHex(array: Uint8Array): string {
    return Array.from(array)
      .map((byte) => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  private static auditError(error: any, operation: string): void {
    console.log(`Crypto error during ${operation}`, error);
  }

  private static async SecurityException(message: string): Promise<void> {
    console.log(`SecurityException: ${message}`);
  }
}
