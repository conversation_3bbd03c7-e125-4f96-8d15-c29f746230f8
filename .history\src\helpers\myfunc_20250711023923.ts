// src/helpers/myfunc.ts

interface CookieOptions {
  expires?: Date | number;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

/**
 * Définit un cookie sécurisé
 */
export function setSecureCookie(
  name: string,
  value: string,
  options: CookieOptions = {}
): void {
  if (typeof document === 'undefined') return;

  // Validation des entrées
  if (!name || !/^[a-zA-Z0-9_-]+$/.test(name)) {
    throw new Error('Nom de cookie invalide');
  }

  const encodedValue = encodeURIComponent(value);
  let cookieString = `${name}=${encodedValue}`;

  if (options.expires) {
    const expires = typeof options.expires === 'number'
      ? new Date(Date.now() + options.expires * 3600000)
      : options.expires;
    cookieString += `; expires=${expires.toUTCString()}`;
  }

  cookieString += `; path=${options.path || '/'}`;

  if (options.domain) {
    cookieString += `; domain=${options.domain}`;
  }

  if (options.secure || location.protocol === 'https:') {
    cookieString += '; Secure';
  }

  if (options.sameSite) {
    cookieString += `; SameSite=${options.sameSite}`;
  }

  document.cookie = cookieString;
}

/**
 * Récupère un cookie
 */
export function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;

  const cookies = document.cookie.split('; ');
  for (const cookie of cookies) {
    const [cookieName, cookieValue] = cookie.split('=');
    if (cookieName === name) {
      return decodeURIComponent(cookieValue);
    }
  }
  return null;
}

/**
 * Supprime un cookie
 */
export function deleteCookie(name: string, path: string = '/'): void {
  setSecureCookie(name, '', {
    expires: new Date(0),
    path
  });
}

/**
 * Génère une chaîne aléatoire sécurisée
 */
export function generateSecureRandom(length: number = 32): string {
  const crypto = window.crypto || (window as any).msCrypto;
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';

  if (crypto?.getRandomValues) {
    const values = new Uint32Array(length);
    crypto.getRandomValues(values);
    for (let i = 0; i < length; i++) {
      result += charset[values[i] % charset.length];
    }
  } else {
    console.warn('Crypto API non disponible, utilisation de Math.random()');
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
  }

  return result;
}
