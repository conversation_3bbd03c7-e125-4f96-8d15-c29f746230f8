// src/utils/http.ts
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { Response } from '../models';
import { tokenService } from 'src/services/token.service';
import { getCookie } from 'src/helpers/myfunc';

// Configuration Axios sécurisée
const httpClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api-iziway.benilife.xyz/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Content-Type-Options': 'nosniff'
  },
  withCredentials: true // Pour les cookies HttpOnly
});

// Intercepteur pour ajouter le token
httpClient.interceptors.request.use(async (config) => {
  const token = await tokenService.getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Protection CSRF si nécessaire
  const csrfToken = getCookie('XSRF-TOKEN');
  if (csrfToken) {
    config.headers['X-XSRF-TOKEN'] = csrfToken;
  }

  return config;
});

// Intercepteur pour gérer les erreurs
httpClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    if (error.response?.status === 401) {
      tokenService.clearToken();
      window.location.href = '/auth/login';
    }

    return Promise.reject(normalizeError(error));
  }
);

/**
 * Interface pour les données d'erreur de l'API
 */
interface ApiErrorData {
  message?: string;
  errors?: any;
}

/**
 * Normalise les erreurs Axios
 */
function normalizeError(error: AxiosError): Response {
  console.error('API Error:', error);

  if (error.response) {
    const errorData = error.response.data as ApiErrorData;
    return {
      success: false,
      message: errorData?.message || 'Erreur serveur',
      result: null,
      errors: errorData?.errors || null
    };
  }

  return {
    success: false,
    message: error.message || 'Erreur de connexion',
    result: null,
    errors: 'Erreur réseau'
  };
}

/**
 * Fonctions API sécurisées
 */
export const api = {
  async get(url: string, params?: Record<string, any>): Promise<Response> {
    try {
      const response = await httpClient.get(url, { params });
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async post(url: string, payload: any, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.post(url, payload, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async postWithFile(url: string, formData: FormData): Promise<Response> {
    try {
      const response = await httpClient.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  // ... autres méthodes si nécessaire
  async put(url: string, payload: any, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.put(url, payload, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async delete(url: string, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.delete(url, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  }
};

