<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { date } from 'quasar';
import { get_date_format, get_amount_format } from 'src/helpers/utils';
import { adminStore } from 'src/stores/core/adminStore';
export default defineComponent({
  name: "ListVenteCarnet",

  setup() {
    const today = date.formatDate(Date.now(), 'DD-MM-YYYY');

    const store = adminStore();
    const { getSubscriptionsByPeriod, getSubscriptionsByFilter } = store;
    const loading = ref(true);
    const period = ref("month");
    const periods = ref([
      { label: 'Jour', value: 'day' },
      { label: 'Mois', value: 'month' },
    ]);
    const subscriptions = ref<any[]>([]);
    const initialPagination = ref({
      sortBy: 'name',
      descending: true,
      page: 1,
      rowsPerPage: 10
    });
    const filterDialog = ref(false);
    const dateRange = ref({
      from: date.formatDate(date.subtractFromDate(new Date(), { days: 30 }), 'YYYY-MM-DD'),
      to: date.formatDate(new Date(), 'YYYY-MM-DD')
    });


    const headers = ref([
      { name: 'client', label: 'CLIENT', field: 'client', sortable: true, align: "left" },
      { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true, align: "left" },
      { name: 'pack', label: 'CARNET(PACK)', field: 'pack', sortable: true, align: "left" },
      { name: 'tarif', label: 'TARIF', field: 'tarif', sortable: true, align: "left" },
      { name: 'amount', label: 'MONTANT', field: 'amount', sortable: true, align: "left" },
      { name: 'payment_date', label: 'DATE PAIEMENT', field: 'payment_date', sortable: true, align: "left" },
    ]) as any;

    const filterPaymentByPeriod = async (selectedPeriod: string) => {
      loading.value = true;
      const res = await getSubscriptionsByFilter({ period: selectedPeriod });
      loading.value = false;
      if (res.success) {
        // Vérification si res.result est un tableau
        if (Array.isArray(res.result.data)) {
          subscriptions.value = res.result.data.map((sub: any) => ({
            ...sub,
            client: `${sub.client.nom} ${sub.client.prenoms}`,
            collector: `${sub.collector.nom} ${sub.collector.prenoms}`,
            pack: sub.pack.name,
            keys: sub.keys || 0,
            tarif: get_amount_format(sub.price),
            amount: get_amount_format(sub.carnet_price),
            payment_date: get_date_format(sub.created_at)
          }));
        } else {
          console.log("res.result n'est pas un tableau", res.result);
        }
      } else {
        console.log("error", res);
      }
    };

    const filterPaymentByDate = async () => {
      loading.value = true;

      console.log("filter data by date");

      if (dateRange.value.from !== null && dateRange.value.to !== null) {
        const res = await getSubscriptionsByFilter({ from: dateRange.value.from, to: dateRange.value.to });
        loading.value = false;
        filterDialog.value = false;
        if (res.success) {
          subscriptions.value = res.result.data.map((sub: any) => ({
            ...sub,
            client: `${sub.client.nom} ${sub.client.prenoms}`,
            collector: `${sub.collector.nom} ${sub.collector.prenoms}`,
            pack: sub.pack.name,
            keys: sub.keys || 0,
            tarif: get_amount_format(sub.price),
            amount: get_amount_format(sub.carnet_price),
            payment_date: get_date_format(sub.created_at)
          }));
        } else {
          console.log("error", res);
        }
      }
    }

    onMounted(async () => {
      await filterPaymentByPeriod(period.value);
    });

    return {
      today, get_date_format, get_amount_format, loading, subscriptions, period, initialPagination,
      periods, headers, filterPaymentByPeriod, dateRange, filterPaymentByDate, filterDialog
    };
  }
});
</script>

<template>
  <div class="q-pa-sm" style="max-width: 100%">
    <q-table flat  title="Dernières ventes de carnets" :rows="subscriptions"
      :columns="headers" :loading="loading" row-key="id" :pagination="initialPagination" table-style="max-width: 100%;">

      <template v-slot:top-right="props">
        <q-select v-model="period" label="Période" dense :options="periods" emit-value map-options option-label="label"
          options-dense class="q-ml-md" style="width: 150px" @update:model-value="filterPaymentByPeriod" />
        <q-btn flat color="primary" icon="search" @click="filterDialog = true" class="q-ml-md" />
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
      </template>

      <template v-slot:no-data="{ icon, message }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
        </div>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="client" :props="props">
            {{ props.row.client }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.collector }}
          </q-td>
          <q-td key="pack" :props="props">
            {{ props.row.pack }}
          </q-td>
          <q-td key="keys" :props="props">
            {{ props.row.keys }}
          </q-td>
          <q-td key="tarif" :props="props">
            {{ props.row.tarif }}
          </q-td>
          <q-td key="amount" :props="props">
            {{ props.row.amount }}
          </q-td>
          <q-td key="payment_date" :props="props">
            {{ props.row.payment_date }}
          </q-td>
        </q-tr>
      </template>
    </q-table>

    <div>
      <q-dialog v-model="filterDialog" persistent>
        <q-card>

          <q-card-section class="">
            <div class="text-h6">Filtrer les ventes de carnets</div>
            <div class="row q-col-gutter-sm">
              <div class="col-md-6 col-sm-12 col-xs-12">
                <q-input outlined v-model="dateRange.from" label="De" dense class="q-ml-md"
                  :rules="[val => !!val || 'Veuillez entrer une date']" lazy-rules placeholder="Entrer une date"
                  autocomplete="off" type="date">
                  <template v-slot:prepend>
                    <q-icon name="event" />
                  </template>
                </q-input>
              </div>
              <div class="col-md-6 col-sm-12 col-xs-12">
                <q-input outlined v-model="dateRange.to" label="A" dense class="q-ml-md"
                  :rules="[val => !!val || 'Veuillez entrer une date']" lazy-rules placeholder="Entrer une date"
                  autocomplete="off" type="date">
                  <template v-slot:prepend>
                    <q-icon name="event" />
                  </template>
                </q-input>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="FERMER" color="negative" v-close-popup />
            <q-btn flat label="RECHERCHER" color="primary" @click="filterPaymentByDate" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </div>
</template>
