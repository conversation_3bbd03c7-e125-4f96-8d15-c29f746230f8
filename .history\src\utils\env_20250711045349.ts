// src/utils/env.ts
import { z } from 'zod'

const envSchema = z.object({
  API_URL: z.string().min(1, "L'URL de l'API est requise"),
  ENCRYPTION_KEY: z.string().min(32, "La clé de chiffrement doit contenir au moins 32 caractères"),
  SALT: z.string().min(32, "Le salt doit contenir au moins 32 caractères"),
  MODE: z.enum(['development', 'production']).default('development'),
  DEV: z.coerce.boolean().default(true),
})

// Valeurs par défaut sécurisées pour le développement
const defaultEncryptionKey = 'iziway-default-encryption-key-2024-secure-development-mode';
const defaultSalt = 'iziway-default-salt-key-2024-secure-development-mode-salt';

// Configuration des variables d'environnement avec gestion d'erreurs
const envConfig = {
  API_URL: import.meta.env.VITE_API_URL ||
    process.env.API_URL ||
    import.meta.env.VITE_API_BASE_URL ||
    'http://127.0.0.1:8000/api/',

  ENCRYPTION_KEY: import.meta.env.VITE_ENCRYPTION_KEY ||
    process.env.ENCRYPTION_KEY ||
    defaultEncryptionKey,

  SALT: import.meta.env.VITE_SALT ||
    process.env.SALT ||
    defaultSalt,

  MODE: (import.meta.env.MODE as 'development' | 'production') ||
    (process.env.NODE_ENV as 'development' | 'production') ||
    'development',

  DEV: import.meta.env.DEV ??
    (process.env.NODE_ENV !== 'production')
};

// Validation avec gestion d'erreurs détaillée
let env: z.infer<typeof envSchema>;

try {
  env = envSchema.parse(envConfig);
} catch (error) {
  if (error instanceof z.ZodError) {
    console.error('❌ Erreur de configuration des variables d\'environnement:');
    error.issues.forEach((issue) => {
      console.error(`  - ${issue.path.join('.')}: ${issue.message}`);
    });

    // En mode développement, utiliser des valeurs par défaut
    if (envConfig.MODE === 'development') {
      console.warn('⚠️  Utilisation des valeurs par défaut pour le développement');
      env = {
        API_URL: envConfig.API_URL,
        ENCRYPTION_KEY: defaultEncryptionKey,
        SALT: defaultSalt,
        MODE: 'development' as const,
        DEV: true
      };
    } else {
      throw new Error('Variables d\'environnement manquantes en production');
    }
  } else {
    throw error;
  }
}

export { env };
