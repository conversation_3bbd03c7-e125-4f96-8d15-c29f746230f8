<script lang="ts">
  import { storeToRefs } from 'pinia';
  import { adminStore } from 'src/stores/core/adminStore';
  import {PropType, defineComponent,onMounted,ref, toRefs} from 'vue';
  import {get_amount_format,get_date_format} from 'src/helpers/utils';
import { Versement } from 'src/models';

  export default defineComponent({
    name: "CollectorVersementTable",
    props:{
      versements: {
        type: Array as PropType<Versement[]>,
        required: true
      }
    },
    setup(props){
      const initialPagination = ref({
        sortBy: 'name',
        descending: true,
        page: 1,
        rowsPerPage: 5
      });
      const filter = ref('');
      const loading = ref(false);


      const headers = [
        { name: 'id', label: 'ID', field: 'id', sortable: true,hide:true },
        { name: 'qrcode', label: 'CODE VERSMENT', field: 'qrcode', sortable: true },
        { name: 'agency', label: 'AGENCE ', field: 'description', sortable: true },
        { name: 'cashier', label: 'AGENT CAISSIER', field: 'cashier', sortable: true },
        { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true },
        { name: 'amount', label: 'TOTAL VERSER', field: 'amount', sortable: true },
        { name: 'amount_remaining', label: 'MONTANT RESTANT', field: 'amount_remaining', sortable: true },
        { name: 'payment_date', label: 'DATE VERSEMENT', field: 'payment_date', sortable: true },
        { name: 'confirmed_at', label: 'DATE CONFIRMATION', field: 'confirmed', sortable: true },
        { name: 'status', label: 'STATUS', field: 'status', sortable: true },
        { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
      ] as any;
      const columns_visibles = [
        'qrcode','agency','cashier','collector','amount','amount_remaining','payment_date',
        'confirmed_at','status','actions'
      ];

      const {versements} = toRefs(props);

      onMounted(()=>{

        if (versements.value.length > 0) {
          loading.value = false;
          console.log("versements",versements.value);
        }else{
          setTimeout(() => {
            loading.value = false;
          }, 2500);
        }
      });

      return {
        initialPagination, filter, headers,columns_visibles,versements,get_amount_format,get_date_format
      };

    }
  });
</script>

<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Liste des versements"
      :rows="versements"
      :columns="headers"
      row-key="name"
      :pagination="initialPagination"
      :filter="filter"
      table-style="max-width: 100%;"
      :visible-columns="columns_visibles"
    >
      <template v-slot:top-right="props">
        <q-btn
          flat round dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close" placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="qrcode" :props="props">
            {{ props.row.qrcode }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          <q-td key="cashier" :props="props">
            {{ props.row.cashier?.nom }} {{ props.row.cashier?.prenoms }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.collector?.nom }} {{ props.row.collector?.prenoms }}
          </q-td>
          <q-td key="amount" :props="props">
            {{ get_amount_format(props.row.amount) }}
          </q-td>
          <q-td key="amount_remaining" :props="props">
            {{ get_amount_format(props.row.amount_remaining) }}
          </q-td>
          <q-td key="payment_date" :props="props">
            {{ get_date_format(props.row.payment_date) }}
          </q-td>
          <q-td key="confirmed_at" :props="props">
            {{ get_date_format(props.row.confirmed_at) }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip
              :color="props.row.status !== 'confirmed' ? 'warning' : 'positive'"
              text-color="white"
            >
            {{ props.row.status }}
          </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
  </q-table>
  </div>
</template>


