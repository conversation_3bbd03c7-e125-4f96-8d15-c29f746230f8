<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import QuarterTable from 'src/components/tables/QuarterTable.vue';
  export default defineComponent({
    name: "DistrictPage",
    components: {
      QuarterTable,
    },
    setup(){
      const bread = ref({ pageTitle: "Districts", subTitle: "Gestion des districts" });
      return {
        bread
      }
    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Gestion des quartiers
            </q-toolbar-title>
            <AddCategoryModal class="q-mr-xs" />
          </q-toolbar>
          <QuarterTable/>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
