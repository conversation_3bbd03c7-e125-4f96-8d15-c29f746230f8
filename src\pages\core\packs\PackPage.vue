<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import PackTable from 'src/components/tables/PackTable.vue';
  import AddPackModal from 'src/components/modals/AddPackModal.vue';
  export default defineComponent({
    name: "PackPage",
    components: {
      BreadCrumb,PackTable,AddPackModal
    },
    setup(){

      const bread = ref({
        pageTitle: "Packs",
        subTitle: "Gestion des Packs"
      });

      return {
        bread
      };
    }
  })
</script>
<template>
  <q-page class="q-pa-sm">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat>
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion des Packs (Carnets)
            </q-toolbar-title>
            <AddPackModal class="q-mr-xs" />
          </q-toolbar>
          <PackTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
