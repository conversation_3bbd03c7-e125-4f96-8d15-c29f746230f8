// src/utils/http.ts
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { Response } from '../models';
import { tokenService } from 'src/services/token.service';
import { getCookie } from 'src/helpers/myfunc';
import { env } from 'src/utils/env';

// Configuration Axios sécurisée
const httpClient = axios.create({
  baseURL: env.API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Content-Type-Options': 'nosniff'
  },
  withCredentials: true 
});

// Intercepteur pour ajouter le token
httpClient.interceptors.request.use(async (config) => {
  const token = await tokenService.getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Protection CSRF si nécessaire
  const csrfToken = getCookie('XSRF-TOKEN');
  if (csrfToken) {
    config.headers['X-XSRF-TOKEN'] = csrfToken;
  }

  return config;
});

// Intercepteur pour gérer les erreurs
httpClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    if (error.response?.status === 401) {
      tokenService.clearToken();
      window.location.href = '/auth/login';
    }

    return Promise.reject(normalizeError(error));
  }
);

/**
 * Interface pour les données d'erreur de l'API
 */
interface ApiErrorData {
  message?: string;
  errors?: any;
}

/**
 * Normalise les erreurs Axios
 */
function normalizeError(error: AxiosError): Response {
  console.error('API Error:', error);

  if (error.response) {
    const errorData = error.response.data as ApiErrorData;
    return {
      success: false,
      message: errorData?.message || 'Erreur serveur',
      result: null,
      errors: errorData?.errors || null
    };
  }

  return {
    success: false,
    message: error.message || 'Erreur de connexion',
    result: null,
    errors: 'Erreur réseau'
  };
}

/**
 * Fonctions API sécurisées
 */
export const api = {
  async get(url: string, params?: Record<string, any>): Promise<Response> {
    try {
      const response = await httpClient.get(url, { params });
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async post(url: string, payload: any, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.post(url, payload, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async postWithFile(url: string, formData: FormData): Promise<Response> {
    try {
      const response = await httpClient.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async put(url: string, payload: any, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.put(url, payload, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  },

  async delete(url: string, config?: AxiosRequestConfig): Promise<Response> {
    try {
      const response = await httpClient.delete(url, config);
      return response.data;
    } catch (error) {
      return normalizeError(error as AxiosError);
    }
  }
};

/**
 * postDataWithToken function
 * Send HTTP POST request with authorization bearer
 * @param url
 * @param payload
 * @returns response
 */
export const postDataWithToken = async (url: string, payload?: any): Promise<Response> => {
  return api.post(url, payload);
};

/**
 * getDataWithToken function
 * Send HTTP GET request with authorization bearer
 * @param url
 * @param payload - Optional payload for GET requests (will be sent as query params)
 * @returns response
 */
export const getDataWithToken = async (url: string, payload?: any): Promise<Response> => {
  return api.get(url, payload);
};

/**
 * getDataWithParams function
 * Send HTTP GET request with parameters and authorization bearer
 * @param url
 * @param params
 * @returns response
 */
export const getDataWithParams = async (url: string, params?: Record<string, any>): Promise<Response> => {
  return api.get(url, params);
};

/**
 * postDataWithFile function
 * Send HTTP POST request with file upload
 * @param url
 * @param formData
 * @returns response
 */
export const postDataWithFile = async (url: string, formData: FormData): Promise<Response> => {
  return api.postWithFile(url, formData);
};

/**
 * postData function
 * Send HTTP POST request without token (for public endpoints)
 * @param url
 * @param payload
 * @returns response
 */
export const postData = async (url: string, payload?: any): Promise<Response> => {
  try {
    // Create a temporary client without auth for public endpoints
    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL || 'https://api-iziway.benilife.xyz/api'}/${url}`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    return normalizeError(error as AxiosError);
  }
};

