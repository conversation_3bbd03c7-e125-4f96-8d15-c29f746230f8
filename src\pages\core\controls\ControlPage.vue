<script lang="ts">
  import { defineComponent } from 'vue';
  import BreadCrump from 'layouts/BreadCrumb.vue';
  import ControlTable from 'src/components/tables/ControlTable.vue';
  export default defineComponent({
    name: "ControlPage",
    components:{
      BreadCrump,ControlTable
    },
    setup(){

    }
  })
</script>
<template>
  <q-page class="q-pa-md" >
    <BreadCrump />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Etat des contrôles des agents superviseurs
            </q-toolbar-title>
          </q-toolbar>
          <q-card-section>
            <ControlTable />
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
