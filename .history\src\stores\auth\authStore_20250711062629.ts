import { defineStore } from 'pinia'
import { api } from '../../helpers/http';
import { tokenService } from '../../services/token.service';
import { CryptoService } from '../../services/crypto.service';
import type { User } from '../../models';
import type { Response } from '../../models';
import { dateUtils } from 'src/helpers/dateUtils';

// Fonction pour récupérer les données utilisateur cryptées
async function getStoredUserData(): Promise<User | null> {
  try {
    const encryptedUserData = localStorage.getItem('user');
    if (!encryptedUserData) return null;

    const encryptedData = JSON.parse(encryptedUserData);
    const decryptedData = await CryptoService.decrypt(encryptedData);
    return JSON.parse(decryptedData) as User;
  } catch (error) {
    console.error('Erreur lors du décryptage des données utilisateur:', error);
    localStorage.removeItem('user');
    return null;
  }
}

// Fonction pour stocker les données utilisateur cryptées
async function storeUserData(user: User): Promise<void> {
  try {
    const userData = JSON.stringify(user);
    const encryptedData = await CryptoService.encrypt(userData);
    localStorage.setItem('user', JSON.stringify(encryptedData));
  } catch (error) {
    console.error('Erreur lors du cryptage des données utilisateur:', error);
    throw new Error('USER_ENCRYPTION_FAILED');
  }
}

export const authStore = defineStore('auth', {
  // other options...
  state: () => ({
    user: {} as User,
    loading: false,
    error: null,
    token: null as string | null,
    isInitialized: false,
  }),
  getters: {
    getUser: (state) => {
      return state.user;
    },
    getToken: (state) => {
      return state.token;
    },
    isAuthenticated: (state) => {
      return !!state.token && Object.keys(state.user).length > 0;
    },
    // Supprimé le getter isInitialized pour éviter le conflit avec le state
  },
  actions: {
    /**
     * Initialise le store avec les données cryptées
     */
    async initialize(): Promise<void> {
      if (this.isInitialized) return;

      try {
        // Récupération du token via le TokenService
        const token = await tokenService.getToken();
        if (token) {
          const isValid = await tokenService.isValid();
          if (isValid) {
            this.token = token;

            // Récupération des données utilisateur cryptées
            const userData = await getStoredUserData();
            if (userData) {
              this.user = userData;
            }
          } else {
            // Token invalide, nettoyage
            await this.clearAuthData();
          }
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation du store:', error);
        await this.clearAuthData();
      } finally {
        this.isInitialized = true;
      }
    },

    /**
     * Nettoie toutes les données d'authentification
     */
    async clearAuthData(): Promise<void> {
      this.user = {} as User;
      this.token = null;
      tokenService.clearToken();
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('tokenExpiration');
    },

    async login(payload: any): Promise<Response> {
      try {
        this.loading = true;
        const response = await api.post('auth/login', payload);

        if (response.success) {
          const result = response.result;
          const role_id = result.user.role_id;

          if (role_id === 1 || role_id === 2) {
            // Stockage sécurisé du token avec le TokenService
            await tokenService.setToken(result.token, payload.rememberMe || false);

            // Stockage crypté des données utilisateur
            await storeUserData(result.user);

            // Mise à jour du state
            this.user = result.user;
            this.token = result.token;

            // Stockage de l'expiration (pour compatibilité)
            const expirationTime = new Date(Date.now() + 8 * 60 * 60 * 1000).toUTCString();
            const tokenExpiration = dateUtils.convertToTime(expirationTime) as any;
            localStorage.setItem('tokenExpiration', tokenExpiration);

            return response;
          } else {
            return {
              success: false,
              message: "Vous n'avez pas le privilège de vous connecter",
              result: null,
              errors: null
            };
          }
        } else {
          return {
            success: false,
            message: response.message,
            result: null,
            errors: response.errors
          };
        }
      } catch (error: any) {
        console.error('Erreur lors de la connexion:', error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: "Erreur de connexion"
        };
      } finally {
        this.loading = false;
      }
    },

    async register_partner(payload: any): Promise<Response> {
      try {
        const response = await api.post('auth/partner/register', payload);
        //this.user = response.result;
        return response;

      } catch (error: any) {
        return error;
      }
    },

    async verify_code(payload: any): Promise<Response> {
      const response = await api.post('auth/partner/verify-account', payload);
      //this.user = response.result;
      return response;
    },

    async logout(): Promise<Response> {
      try {
        this.loading = true;
        const response = await api.post('auth/logout', {});

        if (response.success) {
          // Nettoyage sécurisé de toutes les données
          // await this.clearAuthData();
          return response;
        } else {
          return {
            success: false,
            message: 'Erreur de déconnexion veuillez réessayer plus tard',
            result: null,
          };
        }
      } catch (error: any) {
        console.error('Erreur lors de la déconnexion:', error);
        // En cas d'erreur, on nettoie quand même les données locales
        await this.clearAuthData();
        return {
          success: false,
          message: 'Erreur de déconnexion, données locales nettoyées',
          result: null,
        };
      } finally {
        this.loading = false;
      }
    },

    async register_user(payload: any): Promise<Response> {
      try {
        const response = await api.post('auth/user/register', payload);
        //this.user = response.result;
        return response;

      } catch (error: any) {
        return error;
      }
    }


  }
});
