<script lang="ts">
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent, ref, onMounted } from 'vue';
import { formatDate } from 'src/helpers/utils';

export default defineComponent({
  name: 'BestSellingCollector',
  setup() {
    const collectors = ref<any[]>([]);
    const loading = ref(false);
    const store = adminStore();

    const getCollectors = async () => {
      loading.value = true;
      try {
        const response = await store.getTopCollectors({ year: new Date().getFullYear() });
        // console.log("best collectors", response);
        if (response.success) {
          collectors.value = response.result;
        }
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    };

    const formatPhoneNumber = (phone: string) => {
      return phone.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4');
    };

    onMounted(() => {
      getCollectors();
    });

    return {
      collectors,
      loading,
      formatPhoneNumber,
      formatDate
    };
  }
});
</script>

<template>
  <q-card class="q-pa-md" flat style="height: 420px;">
    <div class="row items-center q-mb-md">
      <div class="col">
        <h5 class="text-h6 q-ma-none text-weight-small text-grey-8">Top 10 des Collecteurs en vente de carnets</h5>
      </div>
    </div>

    <q-scroll-area style="height: 300px;">
      <q-spinner v-if="loading" color="primary" size="3em" />

      <div v-if="!loading && collectors.length === 0" class="text-center text-grey-5 q-mt-xl">
        <q-icon name="star" size="48px" class="q-mb-md" />
        <div>Aucun collecteur actif</div>
      </div>

      <div class="q-gutter-sm">
        <div v-for="(collector) in collectors" :key="collector.id" class="row items-center q-py-sm">
          <div class="col-auto">
            <q-avatar size="40px" color="grey-3">
              <q-icon name="person" color="grey-6" />
            </q-avatar>
          </div>

          <div class="col q-ml-sm">
            <div class="text-body2 text-weight-medium">{{ collector.last_name }} {{ collector.first_name }}</div>
            <div class="text-caption text-grey-6">{{ formatPhoneNumber(collector.phone) }}</div>
            <div class="text-caption text-grey-6" v-if="collector.last_subscription_date">Dernière vente: {{ formatDate(collector.last_subscription_date) }}</div>
          </div>

          <div class="col-auto text-right">
            <div class="text-body2 text-weight-bold text-blue-6">
              {{ collector.total_subscriptions }} carnets
            </div>
            <div class="text-caption text-grey-6">
              {{ collector.total_revenue.toLocaleString() }} FCFA
            </div>
          </div>
        </div>
      </div>
    </q-scroll-area>
  </q-card>
</template>
