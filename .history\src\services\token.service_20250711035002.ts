// src/services/tokenService.ts
import { setSecureCookie, getCookie, deleteCookie } from '../helpers/myfunc';
import { CryptoService } from './crypto.service';

interface TokenPayload {
  exp?: number;
  [key: string]: any;
}

interface EncryptedData {
  iv: string;
  cipher: string;
  authTag: string;
}

class TokenService {
  private memoryToken: string | null = null;

  /**
   * Définit le token de manière sécurisée
   * @param token - Le token JWT
   * @param rememberMe - Persister le token plus longtemps
   */
  setToken(token: string, rememberMe: boolean = false): void {
    // Stockage principal dans un cookie sécurisé
    setSecureCookie('auth_token', token, {
      expires: rememberMe ? 168 : 8, // 7 jours ou 8 heures
      secure: true,
      sameSite: 'Strict',
      path: '/'
    });

    // Stockage secondaire en mémoire
    this.memoryToken = token;
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON> le token
   * @returns Le token ou null si absent/invalide
   */
  getToken(): string | null {
    return getCookie('auth_token') || this.memoryToken;
  }

  /**
   * Vérifie la validité du token
   * @returns true si le token est valide
   */
  isValid(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = this.parsePayload(token);
      return !this.isExpired(payload);
    } catch {
      this.clearToken();
      return false;
    }
  }

  /**
   * Supprime le token de tous les stockages
   */
  clearToken(): void {
    deleteCookie('auth_token');
    this.memoryToken = null;
  }

  /**
   * Récupère les données du payload
   * @returns Les données du payload ou null
   */
  getPayload(): TokenPayload | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      return this.parsePayload(token);
    } catch {
      return null;
    }
  }

  private parsePayload(token: string): TokenPayload {
    this.validateTokenFormat(token);
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    return JSON.parse(atob(base64));
  }

  private validateTokenFormat(token: string): void {
    if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(token)) {
      throw new Error('Format de token invalide');
    }
  }

  private isExpired(payload: TokenPayload): boolean {
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      this.clearToken();
      return true;
    }
    return false;
  }
}

// Exportez une instance singleton
export const tokenService = new TokenService();
