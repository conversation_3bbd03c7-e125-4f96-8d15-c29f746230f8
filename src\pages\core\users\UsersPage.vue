<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import UsersTable from 'src/components/tables/UsersTable.vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import AddUserModal from 'src/components/modals/AddUserModal.vue';
  export default defineComponent({
    name: "UsersPage",
    components: {
      UsersTable,BreadCrumb,AddUserModal
    },
    setup(){
      const bread = ref({
        pageTitle: "Utilisateurs",
        subTitle: "Gestion des Utilisateurs"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm"> 
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat bordered>
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion des Utilisateurs
            </q-toolbar-title>
            
            <q-btn flat round dense icon="refresh" class="q-mr-xs" />
            <!-- <AddUserModal /> -->
          </q-toolbar>
          <UsersTable />
        </q-card>
      </div>
    </div>

  </q-page>
</template>
